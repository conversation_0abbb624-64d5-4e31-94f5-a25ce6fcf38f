# Ubuntu 系统一键部署指南

## 系统要求

### 支持的Ubuntu版本
- Ubuntu 20.04 LTS
- Ubuntu 22.04 LTS
- Ubuntu 24.04 LTS

### 最低硬件要求
- **CPU**: 1核心（推荐2核心）
- **内存**: 2GB RAM（推荐4GB）
- **存储**: 10GB可用空间（推荐20GB）
- **网络**: 稳定的互联网连接

## 快速部署

### 1. 准备服务器

确保您的Ubuntu服务器：
- 已连接到互联网
- 具有root权限或sudo权限
- 防火墙允许HTTP(80)和HTTPS(443)端口

### 2. 下载项目

```bash
# 克隆项目到服务器
git clone <repository-url> lemomate
cd lemomate
```

### 3. 一键部署

```bash
# 设置脚本权限
chmod +x *.sh

# 执行部署（需要root权限）
sudo ./deploy.sh
```

### 4. 部署过程

部署脚本会自动执行以下操作：

1. **系统检查**
   - 检测Ubuntu系统版本
   - 验证网络连接
   - 检查磁盘空间和内存

2. **软件安装**
   - 更新系统软件包
   - 安装Java 11 (OpenJDK)
   - 安装Maven
   - 安装MySQL 8.0
   - 安装Nginx
   - 安装Node.js 16+
   - 安装SSL证书工具

3. **数据库配置**
   - 创建lemomate数据库
   - 创建数据库用户
   - 设置安全密码

4. **应用构建**
   - 构建Spring Boot后端
   - 构建Vue.js前端

5. **服务配置**
   - 配置Nginx反向代理
   - 创建系统服务
   - 配置SSL证书（如果域名可用）

6. **启动服务**
   - 启动所有相关服务
   - 验证部署状态

### 5. 部署完成

部署成功后，您将看到：

```
🎉 恭喜！部署完成！

应用访问信息：
- 应用地址: https://schedulemeet.lemomate.com (如果SSL配置成功)
- 备用地址: http://YOUR_SERVER_IP (通过服务器IP访问)
- 后端API: http://YOUR_SERVER_IP/api

数据库信息：
- 数据库名: lemomate
- 用户名: lemomate
- 密码: [随机生成的密码]
```

**重要**: 请保存显示的数据库密码！

## 访问应用

### 通过域名访问（推荐）
如果您已配置域名 `schedulemeet.lemomate.com` 指向服务器IP：
- https://schedulemeet.lemomate.com

### 通过IP访问
如果没有域名，可以直接通过服务器IP访问：
- http://YOUR_SERVER_IP

## 管理应用

### 常用命令

```bash
# 查看应用状态
sudo ./status.sh

# 启动应用
sudo ./start.sh

# 停止应用
sudo ./stop.sh

# 更新应用
sudo ./update.sh

# 查看日志
journalctl -u lemomate -f
```

### 系统服务管理

```bash
# 查看服务状态
systemctl status lemomate

# 重启服务
systemctl restart lemomate

# 查看服务日志
journalctl -u lemomate -f
```

## 故障排除

### 常见问题

#### 1. 部署失败：网络连接问题
```bash
# 检查网络连接
ping google.com
ping baidu.com

# 检查DNS设置
cat /etc/resolv.conf
```

#### 2. Java安装失败
```bash
# 手动安装Java 11
sudo apt update
sudo apt install openjdk-11-jdk

# 验证安装
java -version
```

#### 3. MySQL配置问题
```bash
# 检查MySQL状态
systemctl status mysql

# 重启MySQL
systemctl restart mysql

# 检查MySQL日志
tail -f /var/log/mysql/error.log
```

#### 4. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep 8085
netstat -tlnp | grep 80

# 杀死占用进程
sudo kill -9 <PID>
```

#### 5. 权限问题
```bash
# 修复文件权限
sudo chown -R www-data:www-data /var/www/lemomate
sudo chmod 755 /home/<USER>/lemomate.jar
```

### 日志查看

```bash
# 应用日志
journalctl -u lemomate -f

# Nginx访问日志
tail -f /var/log/nginx/access.log

# Nginx错误日志
tail -f /var/log/nginx/error.log

# MySQL错误日志
tail -f /var/log/mysql/error.log
```

## 安全建议

### 1. 防火墙配置
```bash
# 安装UFW防火墙
sudo apt install ufw

# 允许SSH
sudo ufw allow ssh

# 允许HTTP和HTTPS
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 2. 定期备份
```bash
# 备份数据库
mysqldump -u lemomate -p lemomate > lemomate_backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /home/<USER>/uploads/
```

### 3. 系统更新
```bash
# 定期更新系统
sudo apt update && sudo apt upgrade

# 更新应用
sudo ./update.sh
```

## 性能优化

### 1. 内存优化
如果服务器内存较小，可以调整JVM参数：

编辑 `/etc/systemd/system/lemomate.service`：
```ini
ExecStart=/usr/bin/java -jar -Xmx1g -Xms512m -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
```

### 2. 数据库优化
编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`：
```ini
[mysqld]
innodb_buffer_pool_size = 512M
max_connections = 100
```

## 联系支持

如果遇到无法解决的问题：

1. 收集错误日志
2. 记录系统信息（Ubuntu版本、内存、CPU等）
3. 联系技术支持团队

---

**注意**: 此部署脚本专为Ubuntu系统优化，在其他Linux发行版上可能需要调整。
