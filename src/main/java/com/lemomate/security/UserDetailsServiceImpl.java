package com.lemomate.security;

import com.lemomate.model.User;
import com.lemomate.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Optional;

@Service
public class UserDetailsServiceImpl implements UserDetailsService {
    @Autowired
    UserRepository userRepository;

    @Override
    @Transactional
    public UserDetails loadUserByUsername(String usernameOrEmail) throws UsernameNotFoundException {
        User user;

        // 首先尝试通过用户名查找
        Optional<User> userOptional = userRepository.findByUsername(usernameOrEmail);

        if (userOptional.isPresent()) {
            user = userOptional.get();
        } else {
            // 如果用户名查找失败，尝试通过邮箱查找
            user = userRepository.findByEmail(usernameOrEmail)
                    .orElseThrow(() -> new UsernameNotFoundException("未找到用户: " + usernameOrEmail));
        }

        return UserDetailsImpl.build(user);
    }
}
