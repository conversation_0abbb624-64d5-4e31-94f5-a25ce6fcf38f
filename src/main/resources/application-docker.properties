# ?????
spring.datasource.url=***************************************************************************************
spring.datasource.username=root
spring.datasource.password=root
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver

# JPA??
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true
spring.jpa.properties.hibernate.dialect=org.hibernate.dialect.MySQL8Dialect
spring.jpa.properties.hibernate.format_sql=true

# ?????
server.port=8085
server.servlet.context-path=/api

# JWT??
jwt.secret=EldqqV5j3JQax6yaKe3XKKNVQLZRpT2g
jwt.expiration=86400000

# Jitsi??
jitsi.app.id=lemomate_app
jitsi.domain=meeting.lemomate.com

# ??????
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=15MB

# ??????
file.upload-dir=/app/uploads/avatars