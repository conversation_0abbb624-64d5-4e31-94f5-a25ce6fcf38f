#!/bin/bash

# Lemomate 状态检查脚本
echo "🔍 Lemomate 应用状态检查"
echo "================================"

# 检查Lemomate服务状态
echo "📋 服务状态:"
if systemctl is-active --quiet lemomate; then
    echo "✅ Lemomate服务: 运行中"
    echo "   启动时间: $(systemctl show lemomate --property=ActiveEnterTimestamp --value)"
else
    echo "❌ Lemomate服务: 已停止"
fi

# 检查MySQL状态
if systemctl is-active --quiet mysql; then
    echo "✅ MySQL服务: 运行中"
else
    echo "❌ MySQL服务: 已停止"
fi

# 检查Nginx状态
if systemctl is-active --quiet nginx; then
    echo "✅ Nginx服务: 运行中"
else
    echo "❌ Nginx服务: 已停止"
fi

echo ""
echo "🌐 网络状态:"

# 检查端口占用
if netstat -tlnp 2>/dev/null | grep -q ":8085"; then
    echo "✅ 后端端口 8085: 已监听"
else
    echo "❌ 后端端口 8085: 未监听"
fi

if netstat -tlnp 2>/dev/null | grep -q ":80"; then
    echo "✅ HTTP端口 80: 已监听"
else
    echo "❌ HTTP端口 80: 未监听"
fi

if netstat -tlnp 2>/dev/null | grep -q ":443"; then
    echo "✅ HTTPS端口 443: 已监听"
else
    echo "⚠️ HTTPS端口 443: 未监听（可能未配置SSL）"
fi

echo ""
echo "💾 系统资源:"

# 检查内存使用
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
echo "📊 内存使用率: ${MEMORY_USAGE}%"

# 检查磁盘使用
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')
echo "💿 磁盘使用率: ${DISK_USAGE}"

# 检查CPU负载
LOAD_AVG=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
echo "⚡ CPU负载: ${LOAD_AVG}"

echo ""
echo "📁 文件状态:"

# 检查关键文件
if [ -f "/home/<USER>/lemomate.jar" ]; then
    JAR_SIZE=$(du -h /home/<USER>/lemomate.jar | awk '{print $1}')
    echo "✅ 应用JAR文件: 存在 (${JAR_SIZE})"
else
    echo "❌ 应用JAR文件: 不存在"
fi

if [ -d "/home/<USER>/frontend" ]; then
    echo "✅ 前端文件: 存在"
else
    echo "❌ 前端文件: 不存在"
fi

if [ -d "/home/<USER>/uploads" ]; then
    UPLOAD_SIZE=$(du -sh /home/<USER>/uploads 2>/dev/null | awk '{print $1}')
    echo "✅ 上传目录: 存在 (${UPLOAD_SIZE})"
else
    echo "❌ 上传目录: 不存在"
fi

echo ""
echo "🔧 维护命令:"
echo "- 启动服务: sudo ./start.sh"
echo "- 停止服务: sudo ./stop.sh"
echo "- 更新应用: sudo ./update.sh"
echo "- 查看日志: journalctl -u lemomate -f"
echo "- 重启服务: sudo systemctl restart lemomate"

# 如果服务运行中，显示访问地址
if systemctl is-active --quiet lemomate; then
    echo ""
    echo "🌍 访问地址:"
    echo "- https://schedulemeet.lemomate.com"
    
    # 获取服务器IP
    SERVER_IP=$(curl -s ifconfig.me 2>/dev/null || echo "无法获取")
    if [ "$SERVER_IP" != "无法获取" ]; then
        echo "- http://${SERVER_IP} (备用IP访问)"
    fi
fi
