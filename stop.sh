#!/bin/bash

# Lemomate 停止脚本
echo "正在停止 Lemomate 应用..."

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 停止服务
if systemctl is-active --quiet lemomate; then
    echo "停止Lemomate服务..."
    systemctl stop lemomate
    
    # 等待服务完全停止
    sleep 3
    
    if ! systemctl is-active --quiet lemomate; then
        echo "✅ Lemomate服务已成功停止"
    else
        echo "⚠️ 服务可能仍在运行，请检查状态"
    fi
else
    echo "ℹ️ Lemomate服务未运行"
fi

echo ""
echo "其他维护命令:"
echo "- 启动服务: ./start.sh"
echo "- 查看状态: systemctl status lemomate"
echo "- 查看日志: journalctl -u lemomate -f"
