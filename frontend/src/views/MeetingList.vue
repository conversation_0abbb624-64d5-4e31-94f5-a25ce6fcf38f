<template>
  <div class="meeting-list">
    <Navbar />

    <div class="container">
      <div class="page-header">
        <div class="page-title">{{ $t('meeting.list') }}</div>
        <div class="page-actions" v-if="isTeamAdmin || isPlatformAdmin">
          <el-button type="primary" @click="$router.push('/meetings/create')">{{ $t('meeting.create') }}</el-button>
        </div>
      </div>

      <el-card v-if="!userTeam">
        <el-alert
          :title="$t('team.noTeam')"
          type="warning"
          :description="$t('team.noTeamDesc')"
          show-icon
          :closable="false">
        </el-alert>
      </el-card>

      <el-card v-else>
        <el-tabs v-model="activeTab">
          <el-tab-pane :label="$t('meeting.tabs.upcoming')" name="upcoming">
            <el-table
              v-loading="loading"
              :data="upcomingMeetings"
              style="width: 100%"
              :empty-text="$t('meeting.noUpcomingMeetings')">
              <el-table-column prop="title" :label="$t('meeting.meetingTitle')"></el-table-column>
              <el-table-column :label="$t('meeting.startTime')">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.startTime) }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.creator')" width="120">
                <template slot-scope="scope">
                  {{ scope.row.creatorName }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.accessRestriction')" width="120">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.teamOnly ? 'warning' : 'success'">
                    {{ scope.row.teamOnly ? $t('meeting.teamOnly') : $t('meeting.publicMeeting') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="$t('common.actions')" width="180">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="joinMeeting(scope.row.id)">{{ $t('meeting.join') }}</el-button>
                  <el-button
                    size="mini"
                    @click="viewMeetingDetails(scope.row.id)">{{ $t('meeting.viewDetails') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="$t('meeting.tabs.ongoing')" name="ongoing">
            <el-table
              v-loading="loading"
              :data="ongoingMeetings"
              style="width: 100%"
              :empty-text="$t('meeting.noOngoingMeetings')">
              <el-table-column prop="title" :label="$t('meeting.meetingTitle')"></el-table-column>
              <el-table-column :label="$t('meeting.startTime')">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.startTime) }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.creator')" width="120">
                <template slot-scope="scope">
                  {{ scope.row.creatorName }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.accessRestriction')" width="120">
                <template slot-scope="scope">
                  <el-tag :type="scope.row.teamOnly ? 'warning' : 'success'">
                    {{ scope.row.teamOnly ? $t('meeting.teamOnly') : $t('meeting.publicMeeting') }}
                  </el-tag>
                </template>
              </el-table-column>
              <el-table-column :label="$t('common.actions')" width="180">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="success"
                    @click="joinMeeting(scope.row.id)">{{ $t('meeting.join') }}</el-button>
                  <el-button
                    size="mini"
                    @click="viewMeetingDetails(scope.row.id)">{{ $t('meeting.viewDetails') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>

          <el-tab-pane :label="$t('meeting.tabs.ended')" name="ended">
            <el-table
              v-loading="loading"
              :data="endedMeetings"
              style="width: 100%"
              :empty-text="$t('meeting.noEndedMeetings')">
              <el-table-column prop="title" :label="$t('meeting.meetingTitle')"></el-table-column>
              <el-table-column :label="$t('meeting.startTime')">
                <template slot-scope="scope">
                  {{ formatDateTime(scope.row.startTime) }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.endTime')">
                <template slot-scope="scope">
                  {{ scope.row.endTime ? formatDateTime(scope.row.endTime) : $t('common.notSet') }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('meeting.creator')" width="120">
                <template slot-scope="scope">
                  {{ scope.row.creatorName }}
                </template>
              </el-table-column>
              <el-table-column :label="$t('common.actions')" width="100">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    @click="viewMeetingDetails(scope.row.id)">{{ $t('meeting.viewDetails') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import api from '@/services/api'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'MeetingList',
  components: {
    Navbar
  },
  data() {
    return {
      meetings: [],
      activeTab: 'ongoing',
      loading: false
    }
  },
  computed: {
    ...mapGetters(['user', 'userTeam']),
    isTeamAdmin() {
      return this.user.role === 'TEAM_ADMIN'
    },
    isPlatformAdmin() {
      return this.user.role === 'PLATFORM_ADMIN'
    },
    upcomingMeetings() {
      return this.meetings.filter(meeting => meeting.status === 'SCHEDULED')
    },
    ongoingMeetings() {
      return this.meetings.filter(meeting => meeting.status === 'ONGOING')
    },
    endedMeetings() {
      return this.meetings.filter(meeting => meeting.status === 'ENDED')
    }
  },
  methods: {
    fetchMeetings() {
      if (!this.userTeam) return

      this.loading = true
      api.getTeamMeetings()
        .then(response => {
          this.meetings = response.data
        })
        .catch(err => {
          this.$message.error(this.$t('error.fetchMeetingsFailed'))
          console.error(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    joinMeeting(meetingId) {
      // 直接导航到会议加入页面
      this.$router.push(`/meetings/join/${meetingId}`)
    },
    viewMeetingDetails(meetingId) {
      this.$router.push(`/meetings/${meetingId}`)
    },
    formatDateTime(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD HH:mm')
    }
  },
  created() {
    this.fetchMeetings()
  }
}
</script>

<style scoped>
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}
</style>
