<template>
  <div class="register-container">
    <el-card class="register-card">
      <div slot="header" class="card-header">
        <div class="header-content">
          <h2>{{ $t('register.title') }} Lemomate</h2>
          <div class="language-switch">
            <el-dropdown @command="changeLanguage" trigger="click">
              <span class="language-btn">
                <i class="el-icon-s-tools"></i>
                {{ currentLanguage === 'zh-CN' ? '中文' : 'English' }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="zh-CN" :class="{ 'active-language': currentLanguage === 'zh-CN' }">
                  中文
                </el-dropdown-item>
                <el-dropdown-item command="en-US" :class="{ 'active-language': currentLanguage === 'en-US' }">
                  English
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>

      <el-form :model="registerForm" :rules="rules" ref="registerForm" label-width="80px">
        <el-form-item :label="$t('register.username')" prop="username">
          <el-input v-model="registerForm.username" :placeholder="$t('register.username')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('register.realName')" prop="realName">
          <el-input v-model="registerForm.realName" :placeholder="$t('register.realName')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('register.email')" prop="email">
          <el-input v-model="registerForm.email" :placeholder="$t('register.email')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('register.password')" prop="password">
          <el-input v-model="registerForm.password" type="password" :placeholder="$t('register.password')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('register.confirmPassword')" prop="confirmPassword">
          <el-input v-model="registerForm.confirmPassword" type="password" :placeholder="$t('register.confirmPassword')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('register.team')" prop="teamId">
          <el-select v-model="registerForm.teamId" :placeholder="$t('register.selectTeam')" style="width: 100%;">
            <el-option
              v-for="team in teams"
              :key="team.id"
              :label="team.teamName"
              :value="team.id">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm('registerForm')" :loading="loading">{{ $t('register.registerButton') }}</el-button>
          <el-button @click="resetForm('registerForm')">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <div class="login-link">
        <router-link to="/login">{{ $t('register.loginLink') }}</router-link>
      </div>
    </el-card>
  </div>
</template>

<script>
import api from '@/services/api'
import { mapGetters } from 'vuex'

export default {
  name: 'Register',
  computed: {
    ...mapGetters(['language']),
    currentLanguage() {
      return this.language
    }
  },
  data() {
    const validatePass2 = (rule, value, callback) => {
      if (value === '') {
        callback(new Error(this.$t('register.confirmPasswordRequired')))
      } else if (value !== this.registerForm.password) {
        callback(new Error(this.$t('register.passwordMismatch')))
      } else {
        callback()
      }
    }

    return {
      registerForm: {
        username: '',
        realName: '',
        email: '',
        password: '',
        confirmPassword: '',
        teamId: null
      },
      rules: {
        username: [
          { required: true, message: this.$t('register.usernameRequired'), trigger: 'blur' },
          { min: 3, max: 50, message: this.$t('register.usernameLength'), trigger: 'blur' }
        ],
        realName: [
          { required: true, message: this.$t('register.realNameRequired'), trigger: 'blur' },
          { max: 100, message: this.$t('register.realNameRequired'), trigger: 'blur' }
        ],
        email: [
          { required: true, message: this.$t('register.emailRequired'), trigger: 'blur' },
          { type: 'email', message: this.$t('register.emailFormat'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('register.passwordRequired'), trigger: 'blur' },
          { min: 6, max: 100, message: this.$t('register.passwordLength'), trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('register.confirmPasswordRequired'), trigger: 'blur' },
          { validator: validatePass2, trigger: 'blur' }
        ]
      },
      teams: [],
      loading: false
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true

          // 移除确认密码字段
          const userData = { ...this.registerForm }
          delete userData.confirmPassword

          api.register(userData)
            .then(response => {
              this.$message.success(response.data.message || this.$t('register.registerSuccess'))
              this.$router.push('/login')
            })
            .catch(err => {
              this.$message.error(err.response?.data?.message || this.$t('register.registerFailed'))
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    fetchTeams() {
      api.getAllTeams()
        .then(response => {
          this.teams = response.data
        })
        .catch(err => {
          console.error('获取团队列表失败', err)
        })
    },
    changeLanguage(language) {
      this.$store.dispatch('changeLanguage', language)
      this.$i18n.setLocale(language)
      // 刷新页面以应用新语言
      this.$forceUpdate()
    }
  },
  created() {
    this.fetchTeams()
  }
}
</script>

<style scoped>
.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background-color: #f5f7fa;
  padding: 20px 0;
}

.register-card {
  width: 500px;
}

.card-header {
  text-align: center;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  flex: 1;
}

.language-switch {
  margin-left: 20px;
}

.language-btn {
  cursor: pointer;
  color: #409EFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.language-btn:hover {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.language-btn i {
  margin-right: 5px;
}

.active-language {
  color: #409EFF;
  font-weight: bold;
}

.login-link {
  margin-top: 20px;
  text-align: center;
}
</style>
