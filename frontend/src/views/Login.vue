<template>
  <div class="login-container">
    <el-card class="login-card">
      <div slot="header" class="card-header">
        <div class="header-content">
          <h2>{{ $t('login.title') }} Lemomate</h2>
          <div class="language-switch">
            <el-dropdown @command="changeLanguage" trigger="click">
              <span class="language-btn">
                <i class="el-icon-s-tools"></i>
                {{ currentLanguage === 'zh-CN' ? '中文' : 'English' }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="zh-CN" :class="{ 'active-language': currentLanguage === 'zh-CN' }">
                  中文
                </el-dropdown-item>
                <el-dropdown-item command="en-US" :class="{ 'active-language': currentLanguage === 'en-US' }">
                  English
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>

      <el-form :model="loginForm" :rules="rules" ref="loginForm" label-width="80px">
        <el-form-item :label="$t('login.username')" prop="username">
          <el-input v-model="loginForm.username" :placeholder="$t('login.username')"></el-input>
        </el-form-item>

        <el-form-item :label="$t('login.password')" prop="password">
          <el-input v-model="loginForm.password" type="password" :placeholder="$t('login.password')"></el-input>
        </el-form-item>

        <el-form-item>
          <el-button type="primary" @click="submitForm('loginForm')" :loading="loading">{{ $t('login.loginButton') }}</el-button>
          <el-button @click="resetForm('loginForm')">{{ $t('common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <div class="register-link">
        <router-link to="/register">{{ $t('login.registerLink') }}</router-link>
      </div>
    </el-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Login',
  computed: {
    ...mapGetters(['language']),
    currentLanguage() {
      return this.language
    }
  },
  data() {
    return {
      loginForm: {
        username: '',
        password: ''
      },
      rules: {
        username: [
          { required: true, message: this.$t('login.usernameRequired'), trigger: 'blur' },
          { min: 3, max: 50, message: this.$t('register.usernameLength'), trigger: 'blur' }
        ],
        password: [
          { required: true, message: this.$t('login.passwordRequired'), trigger: 'blur' },
          { min: 6, max: 100, message: this.$t('register.passwordLength'), trigger: 'blur' }
        ]
      },
      loading: false
    }
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          this.loading = true
          this.$store.dispatch('login', this.loginForm)
            .then(() => {
              this.$message.success(this.$t('login.loginSuccess'))
              this.$router.push('/')
            })
            .catch(err => {
              this.$message.error(err.response?.data?.message || this.$t('login.loginFailed'))
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    changeLanguage(language) {
      this.$store.dispatch('changeLanguage', language)
      this.$i18n.setLocale(language)
      // 刷新页面以应用新语言
      this.$forceUpdate()
    }
  }
}
</script>

<style scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  background-color: #f5f7fa;
}

.login-card {
  width: 400px;
}

.card-header {
  text-align: center;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-content h2 {
  margin: 0;
  flex: 1;
}

.language-switch {
  margin-left: 20px;
}

.language-btn {
  cursor: pointer;
  color: #409EFF;
  font-size: 14px;
  display: flex;
  align-items: center;
  padding: 5px 10px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  background-color: #fff;
  transition: all 0.3s;
}

.language-btn:hover {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.language-btn i {
  margin-right: 5px;
}

.active-language {
  color: #409EFF;
  font-weight: bold;
}

.register-link {
  margin-top: 20px;
  text-align: center;
}
</style>
