<template>
  <div class="team-members">
    <Navbar />

    <div class="container">
      <div class="page-title">{{ $t('team.members') }}</div>

      <el-card v-if="!userTeam">
        <el-alert
          :title="$t('team.noTeam')"
          type="warning"
          :description="$t('team.noTeamDesc')"
          show-icon
          :closable="false">
        </el-alert>
      </el-card>

      <el-card v-else>
        <div class="team-info">
          <h3>{{ userTeam.name }}</h3>
          <p>{{ $t('team.memberCount') }}: {{ members.length }}</p>
        </div>

        <el-table
          v-loading="loading"
          :data="members"
          style="width: 100%"
          :empty-text="$t('team.noMembers')">
          <el-table-column prop="username" :label="$t('user.username')"></el-table-column>
          <el-table-column prop="realName" :label="$t('user.realName')"></el-table-column>
          <el-table-column prop="email" :label="$t('user.email')"></el-table-column>
          <el-table-column :label="$t('user.role')" width="120">
            <template slot-scope="scope">
              <el-tag :type="getRoleTagType(scope.row.role)">
                {{ getRoleText(scope.row.role) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="$t('common.actions')" width="120" v-if="canManageMembers">
            <template slot-scope="scope">
              <el-button
                v-if="canRemoveMember(scope.row)"
                type="danger"
                size="mini"
                @click="removeMember(scope.row)"
                :loading="scope.row.removing">
                {{ $t('team.remove') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import api from '@/services/api'
import { mapGetters } from 'vuex'

export default {
  name: 'TeamMembers',
  components: {
    Navbar
  },
  data() {
    return {
      members: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters(['userTeam', 'user']),
    canManageMembers() {
      return this.user && (this.user.role === 'TEAM_ADMIN' || this.user.role === 'PLATFORM_ADMIN')
    }
  },
  methods: {
    fetchTeamMembers() {
      if (!this.userTeam) return

      this.loading = true
      api.getTeamMembers()
        .then(response => {
          this.members = response.data
        })
        .catch(err => {
          this.$message.error(this.$t('error.fetchTeamMembersFailed'))
          console.error(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    getRoleText(role) {
      switch (role) {
        case 'USER': return this.$t('user.roles.USER')
        case 'TEAM_ADMIN': return this.$t('user.roles.TEAM_ADMIN')
        case 'PLATFORM_ADMIN': return this.$t('user.roles.PLATFORM_ADMIN')
        default: return this.$t('common.unknown')
      }
    },
    getRoleTagType(role) {
      switch (role) {
        case 'USER': return ''
        case 'TEAM_ADMIN': return 'success'
        case 'PLATFORM_ADMIN': return 'danger'
        default: return 'info'
      }
    },
    canRemoveMember(member) {
      // 不能移除自己（除非是平台管理员）
      if (member.id === this.user.id && this.user.role !== 'PLATFORM_ADMIN') {
        return false
      }
      return true
    },
    removeMember(member) {
      this.$confirm(
        this.$t('team.confirmRemoveMember', { name: member.realName || member.username }),
        this.$t('team.confirmRemoveTitle'),
        {
          confirmButtonText: this.$t('common.confirm'),
          cancelButtonText: this.$t('common.cancel'),
          type: 'warning'
        }
      ).then(() => {
        this.$set(member, 'removing', true)

        api.removeMember(this.userTeam.id, member.id)
          .then(() => {
            this.$message.success(this.$t('team.memberRemoved'))
            this.fetchTeamMembers() // 重新加载成员列表
          })
          .catch(err => {
            console.error(err)
            this.$message.error(err.response?.data?.message || this.$t('team.removeMemberFailed'))
          })
          .finally(() => {
            this.$set(member, 'removing', false)
          })
      }).catch(() => {
        // 用户取消操作
      })
    }
  },
  created() {
    this.fetchTeamMembers()
  }
}
</script>

<style scoped>
.team-info {
  margin-bottom: 20px;
}
</style>
