<template>
  <div class="home">
    <Navbar />

    <div class="container">
      <el-row :gutter="20">
        <el-col :span="24">
          <el-card class="welcome-card">
            <div slot="header" class="welcome-header">
              <div class="logo-container">
                <img src="/images/logo.jpg" alt="Lemomate Logo" class="logo-image">
              </div>
              <h2>{{ $t('home.welcome') }}</h2>
            </div>
            <div class="welcome-content">
              <p>{{ $t('home.description') }}</p>

              <div v-if="!userTeam" class="no-team-warning">
                <el-alert
                  :title="$t('home.noTeam')"
                  type="warning"
                  :description="$t('home.noTeamDesc')"
                  show-icon
                  :closable="false">
                </el-alert>
                <div class="team-selection" v-if="teams.length > 0">
                  <h3>{{ $t('home.availableTeams') }}</h3>
                  <el-table :data="teams" style="width: 100%">
                    <el-table-column prop="teamName" :label="$t('team.name')"></el-table-column>
                    <el-table-column prop="memberCount" :label="$t('team.memberCount')"></el-table-column>
                    <el-table-column :label="$t('common.actions')" width="120">
                      <template slot-scope="scope">
                        <el-button
                          size="mini"
                          type="primary"
                          @click="applyToTeam(scope.row.id)">{{ $t('home.apply') }}</el-button>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>
              </div>

              <div v-else class="team-info">
                <p>{{ $t('home.currentTeam') }} <strong>{{ userTeam.name }}</strong></p>

                <div class="quick-actions">
                  <h3>{{ $t('home.quickActions') }}</h3>
                  <el-button-group>
                    <el-button type="primary" style="margin-right: 10px" @click="$router.push('/meetings')">{{ $t('home.viewMeetings') }}</el-button>
                    <el-button type="primary" style="margin-right: 10px" @click="$router.push('/team/members')">{{ $t('home.viewTeamMembers') }}</el-button>
                    <el-button v-if="isTeamAdmin || isPlatformAdmin" type="success" @click="$router.push('/meetings/create')">{{ $t('home.createMeeting') }}</el-button>
                  </el-button-group>
                </div>
              </div>

              <div v-if="recentMeetings.length > 0" class="recent-meetings">
                <h3>{{ $t('home.recentMeetings') }}</h3>
                <el-table :data="recentMeetings" style="width: 100%">
                  <el-table-column prop="title" :label="$t('meeting.meetingTitle')"></el-table-column>
                  <el-table-column :label="$t('meeting.startTime')">
                    <template slot-scope="scope">
                      {{ formatDateTime(scope.row.startTime) }}
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('common.status')">
                    <template slot-scope="scope">
                      <el-tag :type="getMeetingStatusType(scope.row.status)">
                        {{ getMeetingStatusText(scope.row.status) }}
                      </el-tag>
                    </template>
                  </el-table-column>
                  <el-table-column :label="$t('common.actions')" width="120">
                    <template slot-scope="scope">
                      <el-button
                        size="mini"
                        type="primary"
                        @click="joinMeeting(scope.row.id)">{{ $t('home.join') }}</el-button>
                    </template>
                  </el-table-column>
                </el-table>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import api from '@/services/api'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'Home',
  components: {
    Navbar
  },
  data() {
    return {
      recentMeetings: [],
      teams: []
    }
  },
  computed: {
    ...mapGetters(['user', 'userTeam']),
    isTeamAdmin() {
      return this.user.role === 'TEAM_ADMIN'
    },
    isPlatformAdmin() {
      return this.user.role === 'PLATFORM_ADMIN'
    }
  },
  methods: {
    fetchRecentMeetings() {
      if (this.userTeam) {
        api.getTeamMeetings()
          .then(response => {
            this.recentMeetings = response.data.slice(0, 5) // 只显示最近5个会议
          })
          .catch(err => {
            console.error('获取会议列表失败', err)
          })
      }
    },
    fetchTeams() {
      api.getAllTeams()
        .then(response => {
          this.teams = response.data
        })
        .catch(err => {
          console.error('获取团队列表失败', err)
        })
    },
    applyToTeam(teamId) {
      api.applyToTeam(teamId)
        .then(response => {
          this.$message.success(response.data.message)
        })
        .catch(err => {
          this.$message.error(err.response?.data?.message || this.$t('team.applicationFailed'))
        })
    },
    joinMeeting(meetingId) {
      // 直接导航到会议加入页面
      this.$router.push(`/meetings/join/${meetingId}`)
    },
    formatDateTime(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getMeetingStatusText(status) {
      switch (status) {
        case 'SCHEDULED': return this.$t('meeting.status.scheduled')
        case 'ONGOING': return this.$t('meeting.status.ongoing')
        case 'ENDED': return this.$t('meeting.status.ended')
        default: return this.$t('meeting.status.scheduled')
      }
    },
    getMeetingStatusType(status) {
      switch (status) {
        case 'SCHEDULED': return 'info'
        case 'ONGOING': return 'success'
        case 'ENDED': return 'danger'
        default: return 'info'
      }
    }
  },
  created() {
    this.fetchTeams()
    this.fetchRecentMeetings()
  }
}
</script>

<style scoped>
.welcome-card {
  margin-bottom: 20px;
}

.welcome-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.logo-container {
  margin-bottom: 15px;
  width: 100%;
  display: flex;
  justify-content: center;
}

.logo-image {
  max-width: 350px;
  width: 100%;
  height: auto;
  object-fit: contain;
}

.welcome-content {
  line-height: 1.6;
}

.no-team-warning {
  margin: 20px 0;
}

.team-selection {
  margin-top: 20px;
}

.team-info {
  margin: 20px 0;
}

.quick-actions {
  margin: 20px 0;
}

.recent-meetings {
  margin-top: 30px;
}

@media (max-width: 768px) {
  .logo-image {
    max-width: 280px;
  }
}
</style>
