<template>
  <div class="meeting-join">
    <Navbar />

    <div class="container">
      <el-card v-loading="loading">
        <div v-if="meeting" class="meeting-info">
          <div class="meeting-header">
            <h2>{{ meeting.title }}</h2>
            <el-tag :type="getMeetingStatusType(meeting.status)">
              {{ getMeetingStatusText(meeting.status) }}
            </el-tag>
          </div>

          <el-descriptions :column="2" border>
            <el-descriptions-item :label="$t('meeting.creator')">{{ meeting.creatorName }}</el-descriptions-item>
            <el-descriptions-item :label="$t('meeting.team')">{{ meeting.teamName }}</el-descriptions-item>
            <el-descriptions-item :label="$t('meeting.startTime')">{{ formatDateTime(meeting.startTime) }}</el-descriptions-item>
            <el-descriptions-item :label="$t('meeting.endTime')">{{ meeting.endTime ? formatDateTime(meeting.endTime) : $t('common.notSet') }}</el-descriptions-item>
            <el-descriptions-item :label="$t('meeting.accessRestriction')">
              {{ meeting.teamOnly ? $t('meeting.teamOnly') : $t('meeting.publicMeeting') }}
            </el-descriptions-item>
          </el-descriptions>

          <div v-if="meeting.description" class="meeting-description">
            <h3>{{ $t('meeting.meetingDescription') }}</h3>
            <p>{{ meeting.description }}</p>
          </div>

          <div class="meeting-share" v-if="!error">
            <h3>{{ $t('meeting.meetingLink') }}</h3>
            <div class="share-link-container">
              <el-input
                v-model="shareLink"
                readonly
                :placeholder="$t('meeting.meetingLink')"
                class="share-link-input">
                <el-button slot="append" icon="el-icon-document-copy" @click="copyShareLink">{{ $t('meeting.copy') }}</el-button>
              </el-input>
              <div class="share-tip">{{ $t('meeting.shareTip') }}</div>
            </div>
          </div>

          <div class="meeting-actions" v-if="!error">
            <el-button type="primary" size="large" @click="joinMeeting" :loading="joiningMeeting">
              {{ $t('meeting.joinMeeting') }}
            </el-button>
          </div>
        </div>

        <div v-if="error" class="error-message">
          <el-alert
            :title="error"
            type="error"
            :description="$t('meeting.errorDescription')"
            show-icon
            :closable="false">
          </el-alert>

          <div class="error-actions">
            <el-button @click="$router.push('/meetings')">{{ $t('meeting.backToMeetingList') }}</el-button>
            <el-button v-if="!isLoggedIn" type="primary" @click="$router.push('/login')">{{ $t('navbar.login') }}</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import api from '@/services/api'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'MeetingJoin',
  components: {
    Navbar
  },
  data() {
    return {
      meeting: null,
      loading: true,
      joiningMeeting: false,
      error: null,
      shareLink: ''
    }
  },
  computed: {
    baseUrl() {
      return window.location.origin
    },
    ...mapGetters(['isLoggedIn', 'user', 'userTeam'])
  },
  methods: {
    fetchMeeting() {
      this.loading = true
      this.error = null

      const meetingId = this.$route.params.id

      api.getMeeting(meetingId)
        .then(response => {
          this.meeting = response.data

          // 生成可分享的会议链接
          this.shareLink = `${this.baseUrl}/meetings/join/${this.meeting.id}`

          // 检查会议状态
          if (this.meeting.status === 'ENDED') {
            this.error = this.$t('meeting.meetingEnded')
            return
          }

          // 检查团队限制
          if (this.meeting.teamOnly && (!this.userTeam || this.userTeam.id !== this.meeting.teamId)) {
            this.error = this.$t('meeting.teamOnlyRestriction')
          }
        })
        .catch(err => {
          this.error = err.response?.data?.message || this.$t('meeting.fetchMeetingInfoFailed')
          console.error(err)
        })
        .finally(() => {
          this.loading = false
        })
    },
    joinMeeting() {
      if (this.joiningMeeting) return

      this.joiningMeeting = true
      const meetingId = this.$route.params.id

      api.joinMeeting(meetingId)
        .then(response => {
          if (response.data.success && response.data.data.meetingUrl) {
            window.location.href = response.data.data.meetingUrl
          } else {
            this.error = this.$t('meeting.getMeetingLinkFailed')
          }
        })
        .catch(err => {
          this.error = err.response?.data?.message || this.$t('meeting.joinMeetingFailed')
        })
        .finally(() => {
          this.joiningMeeting = false
        })
    },
    copyShareLink() {
      // 复制链接到剪贴板
      const el = document.createElement('textarea')
      el.value = this.shareLink
      document.body.appendChild(el)
      el.select()
      document.execCommand('copy')
      document.body.removeChild(el)

      this.$message.success(this.$t('meeting.meetingLinkCopied'))
    },
    formatDateTime(dateTime) {
      return moment(dateTime).format('YYYY-MM-DD HH:mm')
    },
    getMeetingStatusText(status) {
      switch (status) {
        case 'SCHEDULED': return this.$t('meeting.status.scheduled')
        case 'ONGOING': return this.$t('meeting.status.ongoing')
        case 'ENDED': return this.$t('meeting.status.ended')
        default: return this.$t('common.unknown')
      }
    },
    getMeetingStatusType(status) {
      switch (status) {
        case 'SCHEDULED': return 'info'
        case 'ONGOING': return 'success'
        case 'ENDED': return 'danger'
        default: return 'info'
      }
    }
  },
  created() {
    if (!this.isLoggedIn) {
      this.error = this.$t('meeting.pleaseLogin')
      this.loading = false
      return
    }

    this.fetchMeeting()
  }
}
</script>

<style scoped>
.meeting-join {
  padding: 20px 0;
}

.meeting-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.meeting-description {
  margin: 20px 0;
}

.meeting-share {
  margin: 30px 0;
  padding: 15px;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.share-link-container {
  margin-top: 10px;
}

.share-link-input {
  width: 100%;
}

.share-tip {
  margin-top: 8px;
  color: #909399;
  font-size: 12px;
}

.meeting-actions {
  margin-top: 30px;
  text-align: center;
}

.error-message {
  margin: 20px 0;
}

.error-actions {
  margin-top: 20px;
  text-align: center;
}
</style>
