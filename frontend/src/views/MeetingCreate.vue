<template>
  <div class="meeting-create">
    <Navbar />

    <div class="container">
      <div class="page-title">{{ $t('meeting.create') }}</div>

      <el-card>
        <el-form :model="meetingForm" :rules="rules" ref="meetingForm" label-width="100px">
          <el-form-item :label="$t('meeting.meetingTitle')" prop="title">
            <el-input v-model="meetingForm.title" :placeholder="$t('meeting.titlePlaceholder')"></el-input>
          </el-form-item>

          <el-form-item :label="$t('meeting.description')" prop="description">
            <el-input type="textarea" v-model="meetingForm.description" :placeholder="$t('meeting.descriptionPlaceholder')"></el-input>
          </el-form-item>

          <el-form-item :label="$t('meeting.meetingType')" prop="meetingType">
            <el-radio-group v-model="meetingForm.meetingType">
              <el-radio label="now">{{ $t('meeting.startNow') }}</el-radio>
              <el-radio label="scheduled">{{ $t('meeting.scheduleMeeting') }}</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item :label="$t('meeting.startTime')" prop="startTime" v-if="meetingForm.meetingType === 'scheduled'">
            <el-date-picker
              v-model="meetingForm.startTime"
              type="datetime"
              :placeholder="$t('meeting.selectStartTime')"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-ddTHH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item :label="$t('meeting.endTime')" prop="endTime" v-if="meetingForm.meetingType === 'scheduled'">
            <el-date-picker
              v-model="meetingForm.endTime"
              type="datetime"
              :placeholder="$t('meeting.selectEndTime')"
              format="yyyy-MM-dd HH:mm"
              value-format="yyyy-MM-ddTHH:mm:ss">
            </el-date-picker>
          </el-form-item>

          <el-form-item :label="$t('meeting.accessRestriction')" prop="teamOnly">
            <el-switch
              v-model="meetingForm.teamOnly"
              :active-text="$t('meeting.teamOnly')"
              :inactive-text="$t('meeting.publicMeeting')">
            </el-switch>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" @click="submitForm('meetingForm')" :loading="loading">{{ $t('meeting.create') }}</el-button>
            <el-button @click="resetForm('meetingForm')">{{ $t('common.reset') }}</el-button>
          </el-form-item>
        </el-form>
      </el-card>
    </div>
  </div>
</template>

<script>
import Navbar from '@/components/Navbar.vue'
import api from '@/services/api'
import { mapGetters } from 'vuex'
import moment from 'moment'

export default {
  name: 'MeetingCreate',
  components: {
    Navbar
  },
  data() {
    return {
      meetingForm: {
        title: '',
        description: '',
        meetingType: 'now',
        startTime: '',
        endTime: '',
        teamOnly: true,
        teamId: null
      },
      rules: {
        title: [
          { required: true, message: this.$t('meeting.titleRequired'), trigger: 'blur' },
          { max: 200, message: this.$t('meeting.titleLength'), trigger: 'blur' }
        ],
        description: [
          { max: 500, message: this.$t('meeting.descriptionLength'), trigger: 'blur' }
        ],
        meetingType: [
          { required: true, message: this.$t('meeting.meetingTypeRequired'), trigger: 'change' }
        ],
        startTime: [
          { required: true, message: this.$t('meeting.startTimeRequired'), trigger: 'change' }
        ],
        endTime: [
          { required: true, message: this.$t('meeting.endTimeRequired'), trigger: 'change' }
        ]
      },
      loading: false
    }
  },
  computed: {
    ...mapGetters(['userTeam'])
  },
  methods: {
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (!this.userTeam) {
            this.$message.error(this.$t('meeting.noTeamError'))
            return
          }

          this.loading = true

          // 准备会议数据
          const meetingData = {
            title: this.meetingForm.title,
            description: this.meetingForm.description,
            teamId: this.userTeam.id,
            teamOnly: this.meetingForm.teamOnly
          }

          // 设置会议时间
          if (this.meetingForm.meetingType === 'now') {
            meetingData.startTime = moment().format('YYYY-MM-DDTHH:mm:ss')
          } else {
            // 转换为 ISO 格式
            meetingData.startTime = moment(this.meetingForm.startTime).format('YYYY-MM-DDTHH:mm:ss')
            if (this.meetingForm.endTime) {
              meetingData.endTime = moment(this.meetingForm.endTime).format('YYYY-MM-DDTHH:mm:ss')
            }
          }

          api.createMeeting(meetingData)
            .then(() => {
              this.$message.success(this.$t('meeting.createSuccess'))
              this.$router.push('/meetings')
            })
            .catch(err => {
              this.$message.error(err.response?.data?.message || this.$t('meeting.createFailed'))
            })
            .finally(() => {
              this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped>
</style>
