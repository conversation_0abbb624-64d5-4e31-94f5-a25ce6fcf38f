import Vue from 'vue'
import zhCN from './locales/zh-CN'
import enUS from './locales/en-US'

const messages = {
  'zh-CN': zhCN,
  'en-US': enUS
}

// 简化的国际化实现
class SimpleI18n {
  constructor() {
    this.locale = localStorage.getItem('language') || 'zh-CN'
    this.messages = messages
    this.vm = new Vue({
      data: {
        locale: this.locale
      }
    })
  }

  t(key) {
    const keys = key.split('.')
    let value = this.messages[this.locale]

    for (const k of keys) {
      if (value && typeof value === 'object') {
        value = value[k]
      } else {
        return key // 如果找不到翻译，返回key本身
      }
    }

    return value || key
  }

  setLocale(locale) {
    this.locale = locale
    this.vm.locale = locale
    localStorage.setItem('language', locale)
  }
}

const i18n = new SimpleI18n()

// 添加全局方法
Vue.prototype.$t = (key) => i18n.t(key)
Vue.prototype.$i18n = i18n

export default i18n
