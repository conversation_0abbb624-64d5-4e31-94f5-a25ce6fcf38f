export default {
  // Common
  common: {
    confirm: 'Confirm',
    cancel: 'Cancel',
    save: 'Save',
    edit: 'Edit',
    delete: 'Delete',
    search: 'Search',
    add: 'Add',
    create: 'Create',
    update: 'Update',
    submit: 'Submit',
    reset: 'Reset',
    back: 'Back',
    loading: 'Loading...',
    noData: 'No Data',
    operation: 'Operation',
    status: 'Status',
    actions: 'Actions',
    success: 'Success',
    error: 'Error',
    warning: 'Warning',
    info: 'Info',
    test: 'Test',
    switch: 'Switch',
    unknown: 'Unknown',
    notSet: 'Not Set'
  },

  // Navbar
  navbar: {
    home: 'Lemomate',
    meetings: 'Meetings',
    teamMembers: 'Team Members',
    profile: 'Profile',
    createMeeting: 'Create Meeting',
    teamApplications: 'Team Applications',
    userManagement: 'User Management',
    teamManagement: 'Team Management',
    logout: 'Logout',
    login: 'Login',
    register: 'Register',
    language: 'Language'
  },

  // Login Page
  login: {
    title: 'Login',
    username: '<PERSON><PERSON><PERSON>',
    password: 'Password',
    loginButton: 'Login',
    registerLink: "Don't have an account? Register now",
    usernameRequired: 'Please enter username',
    passwordRequired: 'Please enter password',
    loginSuccess: 'Login successful',
    loginFailed: 'Login failed, please check username and password'
  },

  // Register Page
  register: {
    title: 'Register',
    username: 'Username',
    realName: 'Real Name',
    email: 'Email',
    password: 'Password',
    confirmPassword: 'Confirm Password',
    team: 'Select Team',
    selectTeam: 'Please select a team',
    registerButton: 'Register',
    loginLink: 'Already have an account? Login now',
    usernameRequired: 'Please enter username',
    usernameLength: 'Username length must be between 3-50 characters',
    realNameRequired: 'Please enter real name',
    emailRequired: 'Please enter email',
    emailFormat: 'Please enter a valid email format',
    passwordRequired: 'Please enter password',
    passwordLength: 'Password length must be between 6-100 characters',
    confirmPasswordRequired: 'Please confirm password',
    passwordMismatch: 'Passwords do not match',
    registerSuccess: 'Registration successful',
    registerFailed: 'Registration failed'
  },

  // Home Page
  home: {
    welcome: 'Welcome to Lemomate Meet',
    description: 'Lemomate Meet is an easy-to-use meeting system that helps you easily create and manage online meetings.',
    noTeam: 'You have not joined any team yet',
    noTeamDesc: 'Please select a team to apply for membership, or contact an administrator to assign you to a team.',
    availableTeams: 'Available teams to apply:',
    apply: 'Apply to Join',
    currentTeam: 'Your current team:',
    quickActions: 'Quick Actions:',
    viewMeetings: 'View Meetings',
    viewTeamMembers: 'View Team Members',
    createMeeting: 'Create Meeting',
    recentMeetings: 'Recent Meetings:',
    noRecentMeetings: 'No recent meetings',
    join: 'Join',
    meetingTime: 'Meeting Time'
  },

  // Meeting Related
  meeting: {
    title: 'Meeting',
    list: 'Meeting List',
    create: 'Create Meeting',
    detail: 'Meeting Details',
    joinMeeting: 'Join Meeting',
    meetingTitle: 'Meeting Title',
    description: 'Description',
    startTime: 'Start Time',
    endTime: 'End Time',
    creator: 'Creator',
    team: 'Team',
    roomName: 'Room Name',
    meetingUrl: 'Meeting URL',
    teamOnly: 'Team Members Only',
    publicMeeting: 'Open to All',
    status: {
      scheduled: 'Scheduled',
      ongoing: 'Ongoing',
      ended: 'Ended'
    },
    tabs: {
      upcoming: 'Upcoming',
      ongoing: 'Ongoing',
      ended: 'Ended'
    },
    noMeetings: 'No meetings',
    noUpcomingMeetings: 'No upcoming meetings',
    noOngoingMeetings: 'No ongoing meetings',
    noEndedMeetings: 'No ended meetings',
    titleRequired: 'Meeting title is required',
    titleLength: 'Meeting title cannot exceed 200 characters',
    descriptionLength: 'Meeting description cannot exceed 500 characters',
    startTimeRequired: 'Start time is required',
    createSuccess: 'Meeting created successfully',
    createFailed: 'Failed to create meeting',
    accessRestriction: 'Access Restriction',
    copyLink: 'Copy Link',
    linkCopied: 'Link copied to clipboard',
    viewDetails: 'View Details',
    join: 'Join',
    meetingEnded: 'Meeting has ended',
    teamOnlyRestriction: 'This meeting is for team members only',
    pleaseLogin: 'Please login first',
    fetchMeetingInfoFailed: 'Failed to fetch meeting information',
    getMeetingLinkFailed: 'Failed to get meeting link',
    joinMeetingFailed: 'Failed to join meeting',
    meetingLinkCopied: 'Meeting link copied to clipboard',
    shareTip: 'Share this link with people who need to join the meeting',
    meetingLink: 'Meeting Link',
    meetingDescription: 'Meeting Description',
    copy: 'Copy',
    errorDescription: 'Please confirm you have permission to join this meeting, or contact the meeting creator for help.',
    backToMeetingList: 'Back to Meeting List',
    statusControl: 'Meeting Status Control',
    statusTip: 'You can modify the meeting status. Please set the status to "Ended" when the meeting is finished.',
    statusUpdated: 'Meeting status updated',
    updateStatusFailed: 'Failed to update meeting status',
    fetchMeetingDetailFailed: 'Failed to fetch meeting details',
    meetingType: 'Meeting Type',
    startNow: 'Start Now',
    scheduleMeeting: 'Schedule Meeting',
    titlePlaceholder: 'Please enter meeting title',
    descriptionPlaceholder: 'Please enter meeting description',
    selectStartTime: 'Select start time',
    selectEndTime: 'Select end time',
    meetingTypeRequired: 'Please select meeting type',
    endTimeRequired: 'Please select end time',
    noTeamError: 'You do not belong to any team and cannot create meetings'
  },

  // Team Related
  team: {
    title: 'Team',
    members: 'Team Members',
    applications: 'Team Applications',
    management: 'Team Management',
    name: 'Team Name',
    maxMembers: 'Max Members',
    memberCount: 'Member Count',
    noTeam: 'You do not belong to any team',
    noTeamDesc: 'Please join a team first to view team members.',
    noMembers: 'No team members',
    noApplications: 'No pending applications',
    username: 'Username',
    realName: 'Name',
    email: 'Email',
    role: 'Role',
    applyTime: 'Apply Time',
    approve: 'Approve',
    reject: 'Reject',
    remove: 'Remove',
    createTeam: 'Create Team',
    teamNameRequired: 'Team name is required',
    teamNameLength: 'Team name length must be between 2-100 characters',
    maxMembersRequired: 'Please set max members',
    createSuccess: 'Team created successfully',
    createFailed: 'Failed to create team',
    applicationApproved: 'Application approved',
    applicationRejected: 'Application rejected',
    memberRemoved: 'Member removed',
    alreadyInTeam: 'You already belong to a team',
    applicationPending: 'You have already applied to join this team, please wait for review',
    applicationSubmitted: 'Application submitted, waiting for team admin review',
    applicationFailed: 'Application failed',
    confirmRemoveMember: 'Are you sure you want to remove member "{name}"?',
    confirmRemoveTitle: 'Confirm Removal',
    removeMemberFailed: 'Failed to remove member'
  },

  // User Related
  user: {
    profile: 'Profile',
    management: 'User Management',
    username: 'Username',
    realName: 'Real Name',
    email: 'Email',
    role: 'Role',
    team: 'Team',
    avatar: 'Avatar',
    changeAvatar: 'Change Avatar',
    updateProfile: 'Update Profile',
    updateSuccess: 'Profile updated successfully',
    updateFailed: 'Failed to update profile',
    avatarUploadSuccess: 'Avatar uploaded successfully',
    avatarUploadFailed: 'Failed to upload avatar',
    avatarSizeLimit: 'Avatar file size cannot exceed 2MB',
    avatarFormatError: 'Avatar must be JPG/PNG format',
    searchPlaceholder: 'Search username, name or email',
    roleUpdated: 'User role updated',
    roleUpdateFailed: 'Failed to update user role',
    teamUpdated: 'User team updated',
    teamUpdateFailed: 'Failed to update user team',
    roles: {
      USER: 'User',
      TEAM_ADMIN: 'Team Admin',
      PLATFORM_ADMIN: 'Platform Admin'
    },
    noTeam: 'None'
  },

  // Error Messages
  error: {
    networkError: 'Network error, please check your connection',
    serverError: 'Server error, please try again later',
    unauthorized: 'Unauthorized, please login again',
    forbidden: 'Insufficient permissions',
    notFound: 'Page not found',
    validationError: 'Data validation failed',
    unknownError: 'Unknown error',
    fetchMeetingsFailed: 'Failed to fetch meetings',
    fetchTeamMembersFailed: 'Failed to fetch team members'
  },

  // Success Messages
  success: {
    operationSuccess: 'Operation successful',
    saveSuccess: 'Saved successfully',
    deleteSuccess: 'Deleted successfully',
    updateSuccess: 'Updated successfully',
    createSuccess: 'Created successfully'
  }
}
