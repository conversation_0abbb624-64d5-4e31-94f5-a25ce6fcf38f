<template>
  <el-menu
    :default-active="activeIndex"
    class="navbar"
    mode="horizontal"
    background-color="#545c64"
    text-color="#fff"
    active-text-color="#ffd04b"
    router
  >
    <el-menu-item index="/">{{ $t('navbar.home') }}</el-menu-item>

    <template v-if="isLoggedIn">
      <el-menu-item index="/meetings">{{ $t('navbar.meetings') }}</el-menu-item>
      <el-menu-item index="/team/members">{{ $t('navbar.teamMembers') }}</el-menu-item>

      <!-- 语言切换按钮 -->
      <el-submenu index="language-menu" style="float: right; margin-right: 10px;">
        <template slot="title">
          <i class="el-icon-s-tools"></i>
          {{ $t('navbar.language') }}
        </template>
        <el-menu-item @click="changeLanguage('zh-CN')">
          <span :class="{ 'active-language': currentLanguage === 'zh-CN' }">中文</span>
        </el-menu-item>
        <el-menu-item @click="changeLanguage('en-US')">
          <span :class="{ 'active-language': currentLanguage === 'en-US' }">English</span>
        </el-menu-item>
      </el-submenu>

      <el-submenu index="user-menu" style="float: right;">
        <template slot="title">
          <div class="user-avatar-menu">
            <img v-if="user.avatarUrl" :src="user.avatarUrl" class="avatar-small" :alt="$t('user.avatar')">
            <span v-else class="avatar-placeholder-small">{{ user.realName ? user.realName.charAt(0).toUpperCase() : '?' }}</span>
            <span class="username">{{ user.realName }}</span>
          </div>
        </template>
        <el-menu-item index="/profile">{{ $t('navbar.profile') }}</el-menu-item>
        <el-menu-item v-if="isTeamAdmin || isPlatformAdmin" index="/meetings/create">{{ $t('navbar.createMeeting') }}</el-menu-item>
        <el-menu-item v-if="isTeamAdmin || isPlatformAdmin" index="/team/applications">{{ $t('navbar.teamApplications') }}</el-menu-item>
        <el-menu-item v-if="isPlatformAdmin" index="/admin/users">{{ $t('navbar.userManagement') }}</el-menu-item>
        <el-menu-item v-if="isPlatformAdmin" index="/admin/teams">{{ $t('navbar.teamManagement') }}</el-menu-item>
        <el-menu-item @click="logout">{{ $t('navbar.logout') }}</el-menu-item>
      </el-submenu>
    </template>

    <template v-else>
      <!-- 语言切换按钮（未登录状态） -->
      <el-submenu index="language-menu-guest" style="float: right; margin-right: 10px;">
        <template slot="title">
          <i class="el-icon-s-tools"></i>
          {{ $t('navbar.language') }}
        </template>
        <el-menu-item @click="changeLanguage('zh-CN')">
          <span :class="{ 'active-language': currentLanguage === 'zh-CN' }">中文</span>
        </el-menu-item>
        <el-menu-item @click="changeLanguage('en-US')">
          <span :class="{ 'active-language': currentLanguage === 'en-US' }">English</span>
        </el-menu-item>
      </el-submenu>

      <el-menu-item index="/login" style="float: right;">{{ $t('navbar.login') }}</el-menu-item>
      <el-menu-item index="/register" style="float: right;">{{ $t('navbar.register') }}</el-menu-item>
    </template>
  </el-menu>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'Navbar',
  data() {
    return {
      activeIndex: '/'
    }
  },
  computed: {
    ...mapGetters(['isLoggedIn', 'user', 'language']),
    isTeamAdmin() {
      return this.user.role === 'TEAM_ADMIN'
    },
    isPlatformAdmin() {
      return this.user.role === 'PLATFORM_ADMIN'
    },
    currentLanguage() {
      return this.language
    }
  },
  methods: {
    logout() {
      this.$store.dispatch('logout')
        .then(() => {
          this.$router.push('/login')
        })
    },
    changeLanguage(language) {
      this.$store.dispatch('changeLanguage', language)
      this.$i18n.setLocale(language)
      // 刷新页面以应用新语言
      this.$forceUpdate()
    }
  },
  created() {
    this.activeIndex = this.$route.path
  }
}
</script>

<style scoped>
.navbar {
  margin-bottom: 20px;
}

.user-avatar-menu {
  display: flex;
  align-items: center;
}

.avatar-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  margin-right: 8px;
  object-fit: cover;
}

.avatar-placeholder-small {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background-color: #409EFF;
  color: white;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 12px;
  margin-right: 8px;
}

.username {
  margin-left: 5px;
}

.active-language {
  color: #409EFF;
  font-weight: bold;
}
</style>
