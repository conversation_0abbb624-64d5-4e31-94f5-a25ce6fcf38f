import axios from 'axios'
// 说明：统一使用 axios.defaults.baseURL（见 `frontend/src/store/index.js`），此处不再拼接二次前缀

// 添加请求拦截器
axios.interceptors.request.use(
  config => {
    const token = localStorage.getItem('token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 添加响应拦截器
axios.interceptors.response.use(
  response => {
    return response
  },
  error => {
    if (error.response && error.response.status === 401) {
      // 未授权，清除token并跳转到登录页
      localStorage.removeItem('token')
      localStorage.removeItem('user')
      window.location.href = '/login'
    }
    return Promise.reject(error)
  }
)

export default {
  // 认证相关
  login(credentials) {
    return axios.post(`/auth/login`, credentials)
  },
  register(user) {
    return axios.post(`/auth/register`, user)
  },

  // 用户相关
  getUserProfile() {
    return axios.get(`/users/profile`)
  },
  getAllUsers() {
    return axios.get(`/users/list`)
  },
  updateUserRole(userId, role) {
    return axios.put(`/users/${userId}/role`, role)
  },
  updateUserTeam(userId, teamId) {
    return axios.put(`/users/${userId}/team`, teamId)
  },
  updateUserProfile(userData) {
    return axios.put(`/users/profile`, userData)
  },
  uploadAvatar(file) {
    const formData = new FormData()
    formData.append('file', file)
    return axios.post(`/users/avatar`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    })
  },

  // 团队相关
  getAllTeams() {
    return axios.get(`/teams/list`)
  },
  createTeam(team) {
    return axios.post(`/teams/create`, team)
  },
  getTeamMembers() {
    return axios.get(`/teams/members`)
  },
  getTeamApplications() {
    return axios.get(`/teams/applications`)
  },
  approveApplication(id) {
    return axios.post(`/teams/applications/${id}/approve`)
  },
  rejectApplication(id) {
    return axios.post(`/teams/applications/${id}/reject`)
  },
  applyToTeam(teamId) {
    return axios.post(`/teams/apply/${teamId}`)
  },
  deleteTeam(teamId) {
    return axios.delete(`/teams/${teamId}`)
  },
  removeMember(teamId, userId) {
    return axios.delete(`/teams/${teamId}/members/${userId}`)
  },

  // 会议相关
  createMeeting(meeting) {
    return axios.post(`/meetings/create`, meeting)
  },
  getTeamMeetings() {
    return axios.get(`/meetings/list`)
  },
  getMeeting(id) {
    return axios.get(`/meetings/${id}`)
  },
  joinMeeting(id) {
    return axios.get(`/meetings/join/${id}`)
  },
  updateMeetingStatus(id, status) {
    return axios.put(`/meetings/status/${id}`, { status })
  }
}
