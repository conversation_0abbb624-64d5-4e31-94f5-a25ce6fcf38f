import Vue from 'vue'
import App from './App.vue'
import router from './router'
import store from './store'
import i18n from './i18n'
import ElementUI from 'element-ui'
import 'element-ui/lib/theme-chalk/index.css'

Vue.config.productionTip = false

// 动态设置Element UI语言
const setElementLocale = () => {
  const locale = i18n.locale === 'zh-CN'
    ? require('element-ui/lib/locale/lang/zh-CN').default
    : require('element-ui/lib/locale/lang/en').default
  Vue.use(ElementUI, { locale })
}

setElementLocale()

// 监听语言变化
i18n.vm.$watch('locale', () => {
  setElementLocale()
})

new Vue({
  router,
  store,
  i18n,
  render: h => h(App)
}).$mount('#app')
