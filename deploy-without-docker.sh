#!/bin/bash

# Lemomate 部署脚本
echo "开始部署Lemomate应用到VPS..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 检查是否为Debian系统
if [ ! -f /etc/debian_version ]; then
    echo "警告：这不是Debian系统，脚本可能不兼容"
    read -p "是否继续？ (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 确保目录存在
echo "创建必要的目录..."
mkdir -p /home/<USER>/uploads/avatars
mkdir -p /home/<USER>/logs

# 安装必要的软件
echo "安装必要的软件..."

# 更新软件包列表
echo "更新软件包列表..."
apt-get update

# 安装Java 11
echo "安装OpenJDK 11..."

# 先尝试从默认仓库安装OpenJDK 11
if apt-cache search openjdk-11-jdk | grep -q openjdk-11-jdk; then
    echo "从默认仓库安装OpenJDK 11..."
    apt-get install -y openjdk-11-jdk
else
    # 如果默认仓库中找不到OpenJDK 11，则添加Adoptium仓库
    echo "默认仓库中未找到OpenJDK 11，添加Adoptium仓库..."

    # 安装添加仓库所需的工具
    apt-get install -y wget apt-transport-https gnupg

    # 添加Adoptium仓库的GPG密钥
    wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | apt-key add -

    # 添加Adoptium仓库
    echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list

    # 更新包列表
    apt-get update

    # 安装Temurin JDK 11（Adoptium的OpenJDK发行版）
    echo "安装Temurin JDK 11..."
    apt-get install -y temurin-11-jdk

    # 如果仍然失败，尝试从其他源下载JDK 11
    if [ $? -ne 0 ]; then
        echo "安装Temurin JDK 11失败，尝试手动下载并安装OpenJDK 11..."

        # 创建临时目录
        mkdir -p /tmp/java-install
        cd /tmp/java-install

        # 下载Adoptium OpenJDK 11
        wget https://github.com/adoptium/temurin11-binaries/releases/download/jdk-11.0.18%2B10/OpenJDK11U-jdk_x64_linux_hotspot_11.0.18_10.tar.gz

        # 解压到/opt目录
        mkdir -p /opt/jdk
        tar -xzf OpenJDK11U-jdk_x64_linux_hotspot_11.0.18_10.tar.gz -C /opt/jdk

        # 设置环境变量
        JAVA_HOME=$(find /opt/jdk -maxdepth 1 -type d -name "jdk-11*")
        echo "export JAVA_HOME=$JAVA_HOME" > /etc/profile.d/jdk11.sh
        echo "export PATH=\$JAVA_HOME/bin:\$PATH" >> /etc/profile.d/jdk11.sh
        chmod +x /etc/profile.d/jdk11.sh
        source /etc/profile.d/jdk11.sh

        # 返回原目录
        cd -
    fi
fi

# 验证Java版本
echo "已安装的Java版本："
java -version

# 确保Java版本是11
JAVA_VERSION=$(java -version 2>&1 | awk -F '"' '/version/ {print $2}' | awk -F '.' '{print $1}')
if [ "$JAVA_VERSION" != "11" ]; then
    echo "错误：需要Java 11，但当前安装的是Java $JAVA_VERSION"
    echo "请手动安装Java 11并重新运行脚本"
    exit 1
fi

# 安装Maven
echo "安装Maven..."
apt-get install -y maven

# 安装MySQL
echo "安装MySQL..."

# 尝试安装MariaDB（Debian 12默认）
if apt-cache search mariadb-server | grep -q mariadb-server; then
    echo "安装MariaDB服务器..."
    apt-get install -y mariadb-server
else
    # 如果找不到MariaDB，则尝试安装MySQL
    echo "尝试安装MySQL服务器..."
    apt-get install -y default-mysql-server || apt-get install -y mysql-server
fi

# 启动MySQL服务
echo "启动MySQL服务..."
systemctl start mysql
systemctl enable mysql

# 安装Nginx
echo "安装Nginx..."
apt-get install -y nginx

# 安装Certbot
echo "安装Certbot..."
apt-get install -y certbot python3-certbot-nginx

# 安装Node.js和npm
echo "安装Node.js和npm..."
apt-get install -y curl

# 尝试安装Node.js 18 LTS
echo "添加Node.js仓库..."
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -

# 安装Node.js
echo "安装Node.js..."
apt-get install -y nodejs

# 验证Node.js和npm版本
echo "Node.js版本:"
node -v
echo "npm版本:"
npm -v

# 配置MySQL
echo "配置MySQL..."

# 设置安全的数据库密码
DB_PASSWORD="your_secure_password"

# 创建数据库和用户
mysql -e "CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
mysql -e "CREATE USER IF NOT EXISTS 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
mysql -e "GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';"
mysql -e "FLUSH PRIVILEGES;"

# 更新数据库配置
echo "更新数据库配置..."
sed -i 's/spring.datasource.username=root/spring.datasource.username=lemomate/g' src/main/resources/application-prod.properties
sed -i "s/spring.datasource.password=root/spring.datasource.password=$DB_PASSWORD/g" src/main/resources/application-prod.properties

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests

# 构建前端
echo "构建前端应用..."
cd frontend

# 检查Node.js和npm版本
echo "Node.js版本:"
node -v
echo "npm版本:"
npm -v

# 安装依赖
echo "安装前端依赖..."
npm install

# 构建前端
echo "构建前端..."
npm run build

cd ..

# 配置Nginx
echo "配置Nginx..."
cat > /etc/nginx/sites-available/lemomate << EOF
# 默认服务器配置，响应所有请求（包括IP地址）
server {
    listen 80 default_server;
    server_name schedulemeet.lemomate.com **************;

    location / {
        root /home/<USER>/frontend;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8085;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /api/uploads/ {
        alias /home/<USER>/uploads/;
        autoindex off;
    }
}
EOF

# 启用站点配置
echo "启用Nginx站点配置..."
ln -sf /etc/nginx/sites-available/lemomate /etc/nginx/sites-enabled/

# 检查Nginx配置
echo "检查Nginx配置..."
nginx -t

# 重启Nginx
echo "重启Nginx..."
systemctl restart nginx

# 获取SSL证书
echo "获取SSL证书..."
certbot --nginx -d schedulemeet.lemomate.com --non-interactive --agree-tos --email <EMAIL>

# 复制前端文件
echo "部署前端文件..."
mkdir -p /home/<USER>/frontend
cp -r frontend/dist/* /home/<USER>/frontend/

# 创建服务文件
echo "创建系统服务..."
cat > /etc/systemd/system/lemomate.service << EOF
[Unit]
Description=Lemomate Backend Service
After=network.target mysql.service

[Service]
User=root
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 复制JAR文件
echo "复制JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar

# 重新加载系统服务
echo "重新加载系统服务..."
systemctl daemon-reload

# 启用服务
echo "启用Lemomate服务..."
systemctl enable lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 检查服务状态
echo "检查Lemomate服务状态..."
systemctl status lemomate

# 检查服务是否成功启动
if systemctl is-active --quiet lemomate; then
    echo "\n\n恭喜！部署完成！"
    echo "\n应用已在 https://schedulemeet.lemomate.com 上线"
    echo "请确保您的域名 schedulemeet.lemomate.com 已经正确解析到服务器IP地址"

    echo "\n重要信息："
    echo "1. 数据库用户名： lemomate"
    echo "2. 数据库密码： $DB_PASSWORD"
    echo "3. 应用访问地址： https://schedulemeet.lemomate.com"
    echo "4. 后端服务运行在端口 8085"

    echo "\n维护命令："
    echo "- 查看日志： journalctl -u lemomate -f"
    echo "- 重启服务： systemctl restart lemomate"
    echo "- 停止服务： systemctl stop lemomate"
    echo "- 启动服务： systemctl start lemomate"
    echo "- 查看状态： systemctl status lemomate"
else
    echo "\n\n警告：Lemomate服务启动失败！"
    echo "请检查日志以获取更多信息： journalctl -u lemomate -f"
fi
