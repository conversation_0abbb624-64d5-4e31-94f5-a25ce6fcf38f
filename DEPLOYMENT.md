# Lemomate 部署指南

## 快速开始

### 1. 系统要求
- Debian 11/12 或 Ubuntu 20.04+
- 2GB+ RAM，10GB+ 存储空间
- root权限

### 2. 一键部署
```bash
# 克隆项目
git clone <repository-url> lemomate
cd lemomate

# 设置权限并部署
make setup
make deploy
```

### 3. 管理应用
```bash
# 查看状态
make status

# 启动/停止服务
make start
make stop

# 更新应用
make update
```

## 手动部署

如果不使用Makefile，可以直接运行脚本：

```bash
# 设置权限
chmod +x *.sh

# 首次部署
sudo ./deploy.sh

# 日常管理
sudo ./start.sh    # 启动
sudo ./stop.sh     # 停止
sudo ./update.sh   # 更新
sudo ./status.sh   # 状态
```

## 访问应用

部署完成后，可通过以下方式访问：

- **主域名**: https://schedulemeet.lemomate.com
- **备用IP**: http://你的服务器IP
- **API接口**: http://你的服务器IP/api

## 故障排除

### 查看日志
```bash
# 应用日志
journalctl -u lemomate -f

# Nginx日志
tail -f /var/log/nginx/error.log

# MySQL日志
tail -f /var/log/mysql/error.log
```

### 常见问题

1. **服务启动失败**
   - 检查端口占用：`netstat -tlnp | grep 8085`
   - 检查Java版本：`java -version`
   - 查看详细日志：`journalctl -u lemomate -f`

2. **前端访问失败**
   - 检查Nginx状态：`systemctl status nginx`
   - 测试配置：`nginx -t`

3. **数据库连接失败**
   - 检查MySQL状态：`systemctl status mysql`
   - 测试连接：`mysql -u lemomate -p lemomate`

## 文件结构

```
lemomate/
├── deploy.sh          # 部署脚本
├── start.sh           # 启动脚本
├── stop.sh            # 停止脚本
├── update.sh          # 更新脚本
├── status.sh          # 状态检查脚本
├── Makefile           # Make命令
├── SCRIPTS.md         # 脚本详细说明
└── DEPLOYMENT.md      # 本文件
```

## 安全建议

1. 定期备份数据库
2. 保存部署时生成的数据库密码
3. 配置防火墙规则
4. 定期更新系统和应用
5. 监控系统资源使用情况

## 技术支持

如需帮助，请：
1. 查看 [SCRIPTS.md](SCRIPTS.md) 详细文档
2. 检查应用日志和系统状态
3. 联系技术支持团队
