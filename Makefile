# Lemomate 项目管理

.PHONY: help setup deploy start stop update status clean

# 默认目标
help:
	@echo "Lemomate 项目管理命令："
	@echo ""
	@echo "  setup   - 设置脚本执行权限"
	@echo "  deploy  - 首次部署应用"
	@echo "  start   - 启动应用服务"
	@echo "  stop    - 停止应用服务"
	@echo "  update  - 更新应用"
	@echo "  status  - 查看应用状态"
	@echo "  clean   - 清理构建文件"
	@echo ""
	@echo "注意：除了 setup 和 clean，其他命令需要 root 权限"

# 设置脚本执行权限
setup:
	@echo "设置脚本执行权限..."
	chmod +x *.sh
	@echo "✅ 权限设置完成"

# 首次部署
deploy: setup
	@echo "开始部署应用..."
	sudo ./deploy.sh

# 启动服务
start: setup
	@echo "启动应用服务..."
	sudo ./start.sh

# 停止服务
stop: setup
	@echo "停止应用服务..."
	sudo ./stop.sh

# 更新应用
update: setup
	@echo "更新应用..."
	sudo ./update.sh

# 查看状态
status: setup
	@echo "查看应用状态..."
	sudo ./status.sh

# 清理构建文件
clean:
	@echo "清理构建文件..."
	mvn clean || true
	rm -rf frontend/dist || true
	rm -rf frontend/node_modules || true
	@echo "✅ 清理完成"
