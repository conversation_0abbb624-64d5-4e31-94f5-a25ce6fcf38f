# 部署前检查清单

在开始部署Lemomate应用之前，请确保满足以下条件：

## ✅ 服务器要求

### 操作系统
- [ ] Ubuntu 20.04 LTS 或更高版本
- [ ] 或 Debian 11/12
- [ ] 系统已更新到最新版本

### 硬件配置
- [ ] CPU: 至少1核心（推荐2核心）
- [ ] 内存: 至少2GB RAM（推荐4GB）
- [ ] 存储: 至少10GB可用空间（推荐20GB）
- [ ] 网络: 稳定的互联网连接

### 权限要求
- [ ] 具有root权限或sudo权限
- [ ] 能够执行系统管理命令

## ✅ 网络配置

### 域名设置（可选但推荐）
- [ ] 已注册域名 `schedulemeet.lemomate.com`
- [ ] 域名已正确解析到服务器IP地址
- [ ] DNS解析生效（可通过 `nslookup schedulemeet.lemomate.com` 验证）

### 防火墙设置
- [ ] 开放HTTP端口 (80)
- [ ] 开放HTTPS端口 (443)
- [ ] 开放SSH端口 (22) 用于管理

### Jitsi Meet服务器
- [ ] 已部署Jitsi Meet服务器
- [ ] Jitsi域名为 `meeting.lemomate.com`
- [ ] Jitsi服务器正常运行

## ✅ 部署准备

### 项目代码
- [ ] 已获取项目源代码
- [ ] 代码已上传到服务器
- [ ] 确认项目目录结构完整

### 配置文件
- [ ] 确认 `application-prod.properties` 配置正确
- [ ] Jitsi域名配置为 `meeting.lemomate.com`
- [ ] 数据库配置准备就绪

### 备份计划
- [ ] 如果是更新部署，已备份现有数据
- [ ] 准备好回滚方案

## ✅ 部署执行

### 脚本权限
```bash
# 检查脚本是否存在
ls -la *.sh

# 设置执行权限
chmod +x *.sh
```

### 系统检查
```bash
# 检查系统版本
lsb_release -a

# 检查可用空间
df -h

# 检查内存
free -h

# 检查网络连接
ping -c 3 google.com
```

### 端口检查
```bash
# 检查端口是否被占用
netstat -tlnp | grep :80
netstat -tlnp | grep :443
netstat -tlnp | grep :8085
netstat -tlnp | grep :3306
```

## ✅ 部署后验证

### 服务状态检查
```bash
# 检查应用状态
sudo ./status.sh

# 检查系统服务
systemctl status lemomate
systemctl status mysql
systemctl status nginx
```

### 功能测试
- [ ] 访问首页正常
- [ ] 用户注册功能正常
- [ ] 用户登录功能正常
- [ ] 创建会议功能正常
- [ ] 会议链接生成正常
- [ ] 文件上传功能正常

### 性能检查
- [ ] 页面加载速度正常
- [ ] API响应时间正常
- [ ] 数据库连接正常

## 🚨 常见问题预防

### 1. 内存不足
如果服务器内存小于2GB：
```bash
# 创建交换文件
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 2. 磁盘空间不足
```bash
# 清理系统缓存
sudo apt clean
sudo apt autoremove

# 检查大文件
du -sh /* | sort -hr | head -10
```

### 3. 网络连接问题
```bash
# 检查DNS设置
cat /etc/resolv.conf

# 测试网络连接
curl -I https://google.com
```

### 4. 权限问题
```bash
# 确保以root用户运行部署脚本
sudo whoami

# 检查文件权限
ls -la /home/<USER>/
```

## 📞 获取帮助

如果在检查过程中发现问题：

1. **查看详细文档**
   - [Ubuntu部署指南](UBUNTU-DEPLOY.md)
   - [脚本使用说明](SCRIPTS.md)

2. **收集系统信息**
   ```bash
   # 系统信息
   uname -a
   lsb_release -a
   
   # 硬件信息
   free -h
   df -h
   
   # 网络信息
   ip addr show
   ```

3. **联系技术支持**
   - 提供系统信息
   - 描述具体问题
   - 附上相关日志

---

**重要提醒**: 
- 部署前请仔细阅读此检查清单
- 确保所有必要条件都已满足
- 建议在测试环境先进行部署验证
- 生产环境部署前请做好数据备份
