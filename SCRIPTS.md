# Lemomate 部署和管理脚本

本项目提供了一套简化的脚本来管理Lemomate应用的部署和运维。

## 脚本列表

### 1. deploy.sh - 部署脚本
**用途**: 首次部署Lemomate应用到VPS服务器

**功能**:
- 自动安装所有依赖（Java 11, Maven, MySQL, Nginx, Node.js等）
- 配置数据库和用户
- 构建前端和后端应用
- 配置Nginx反向代理
- 自动获取SSL证书（如果域名已配置）
- 创建系统服务
- 启动应用

**使用方法**:
```bash
sudo chmod +x deploy.sh
sudo ./deploy.sh
```

**注意事项**:
- 必须以root用户运行
- 适用于Debian/Ubuntu系统
- 首次部署时使用

### 2. update.sh - 更新脚本
**用途**: 更新已部署的Lemomate应用

**功能**:
- 拉取最新代码
- 构建前端和后端
- 备份当前版本
- 部署新版本
- 重启服务

**使用方法**:
```bash
sudo chmod +x update.sh
sudo ./update.sh
```

**注意事项**:
- 必须以root用户运行
- 会自动备份当前版本
- 适用于日常更新

### 3. start.sh - 启动脚本
**用途**: 启动Lemomate应用和相关服务

**功能**:
- 检查并启动MySQL服务
- 检查并启动Nginx服务
- 启动Lemomate应用服务
- 显示访问地址和维护命令

**使用方法**:
```bash
sudo chmod +x start.sh
sudo ./start.sh
```

### 4. stop.sh - 停止脚本
**用途**: 停止Lemomate应用服务

**功能**:
- 优雅停止Lemomate服务
- 显示停止状态
- 提供其他维护命令提示

**使用方法**:
```bash
sudo chmod +x stop.sh
sudo ./stop.sh
```

## 部署流程

### 首次部署
1. 将项目代码上传到服务器
2. 运行部署脚本：`sudo ./deploy.sh`
3. 等待部署完成
4. 访问应用地址测试

### 日常更新
1. 运行更新脚本：`sudo ./update.sh`
2. 等待更新完成
3. 测试新功能

### 服务管理
- 启动服务：`sudo ./start.sh`
- 停止服务：`sudo ./stop.sh`
- 查看状态：`systemctl status lemomate`
- 查看日志：`journalctl -u lemomate -f`

## 系统要求

- **操作系统**: Debian 11/12 或 Ubuntu 20.04+
- **内存**: 至少 2GB RAM
- **存储**: 至少 10GB 可用空间
- **网络**: 需要互联网连接下载依赖

## 端口配置

- **前端**: 通过Nginx代理（80/443端口）
- **后端**: 8085端口（内部）
- **MySQL**: 3306端口（内部）

## 文件路径

- **应用JAR**: `/home/<USER>/lemomate.jar`
- **前端文件**: `/home/<USER>/frontend/`
- **上传文件**: `/home/<USER>/uploads/`
- **日志文件**: `/home/<USER>/logs/`
- **Nginx配置**: `/etc/nginx/sites-available/lemomate`
- **系统服务**: `/etc/systemd/system/lemomate.service`

## 故障排除

### 服务启动失败
```bash
# 查看详细日志
journalctl -u lemomate -f

# 检查端口占用
netstat -tlnp | grep 8085

# 检查Java进程
ps aux | grep java
```

### 前端访问失败
```bash
# 检查Nginx状态
systemctl status nginx

# 检查Nginx配置
nginx -t

# 查看Nginx日志
tail -f /var/log/nginx/error.log
```

### 数据库连接失败
```bash
# 检查MySQL状态
systemctl status mysql

# 测试数据库连接
mysql -u lemomate -p lemomate
```

## 安全建议

1. **定期备份数据库**
2. **保存数据库密码**（部署时会显示）
3. **配置防火墙**（只开放必要端口）
4. **定期更新系统**
5. **监控日志文件**

## 联系支持

如果遇到问题，请：
1. 查看日志文件
2. 检查系统资源
3. 确认网络连接
4. 联系技术支持
