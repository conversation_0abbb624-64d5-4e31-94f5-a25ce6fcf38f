#!/bin/bash

# MySQL配置修复脚本
echo "🔧 修复MySQL配置..."

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 生成随机密码
DB_PASSWORD=$(openssl rand -base64 32)
echo "生成的数据库密码: $DB_PASSWORD"

# 检查MySQL是否运行
if ! systemctl is-active --quiet mysql; then
    echo "启动MySQL服务..."
    systemctl start mysql
    sleep 5
fi

echo "尝试配置MySQL数据库..."

# 方法1: 使用sudo mysql（Ubuntu默认方式）
if sudo mysql -e "SELECT 1;" &>/dev/null; then
    echo "✅ 使用sudo mysql访问方式"
    
    # 创建数据库
    sudo mysql -e "CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # 删除可能存在的用户
    sudo mysql -e "DROP USER IF EXISTS 'lemomate'@'localhost';" 2>/dev/null || true
    
    # 创建用户
    sudo mysql -e "CREATE USER 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
    
    # 授权
    sudo mysql -e "GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';"
    
    # 刷新权限
    sudo mysql -e "FLUSH PRIVILEGES;"
    
    echo "✅ MySQL配置完成"
    
# 方法2: 使用mysql命令（无密码）
elif mysql -u root -e "SELECT 1;" &>/dev/null; then
    echo "✅ 使用mysql root无密码访问方式"
    
    # 创建数据库
    mysql -u root -e "CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"
    
    # 删除可能存在的用户
    mysql -u root -e "DROP USER IF EXISTS 'lemomate'@'localhost';" 2>/dev/null || true
    
    # 创建用户
    mysql -u root -e "CREATE USER 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"
    
    # 授权
    mysql -u root -e "GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';"
    
    # 刷新权限
    mysql -u root -e "FLUSH PRIVILEGES;"
    
    echo "✅ MySQL配置完成"
    
else
    echo "❌ 无法访问MySQL，尝试重置配置..."
    
    # 停止MySQL
    systemctl stop mysql
    
    # 创建临时配置文件
    cat > /tmp/mysql-init.sql << EOF
ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';
CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
DROP USER IF EXISTS 'lemomate'@'localhost';
CREATE USER 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';
GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';
FLUSH PRIVILEGES;
EOF
    
    # 使用安全模式启动MySQL
    echo "使用安全模式重新配置MySQL..."
    mysqld_safe --init-file=/tmp/mysql-init.sql --skip-networking &
    MYSQL_PID=$!
    
    # 等待MySQL启动
    sleep 15
    
    # 停止安全模式的MySQL
    kill $MYSQL_PID 2>/dev/null || true
    pkill -f mysqld_safe 2>/dev/null || true
    pkill -f mysqld 2>/dev/null || true
    sleep 5
    
    # 清理临时文件
    rm -f /tmp/mysql-init.sql
    
    # 重新启动MySQL
    systemctl start mysql
    sleep 5
    
    echo "✅ MySQL重新配置完成"
fi

# 验证配置
echo "验证MySQL配置..."
if mysql -u lemomate -p$DB_PASSWORD -e "SELECT 1;" &>/dev/null; then
    echo "✅ 数据库连接测试成功"
    echo ""
    echo "数据库配置信息："
    echo "- 数据库名: lemomate"
    echo "- 用户名: lemomate"
    echo "- 密码: $DB_PASSWORD"
    echo ""
    echo "请保存这个密码，并更新application-prod.properties文件："
    echo "spring.datasource.username=lemomate"
    echo "spring.datasource.password=$DB_PASSWORD"
else
    echo "❌ 数据库连接测试失败"
    echo "请手动配置MySQL或联系技术支持"
    exit 1
fi

echo ""
echo "🎉 MySQL配置修复完成！"
