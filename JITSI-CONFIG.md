# Jitsi 配置说明

## 概述

Lemomate 系统与 Jitsi Meet 集成，支持通过配置文件或环境变量来设置 Jitsi 域名，方便在不同环境中部署。

## 配置方式

### 1. 通过配置文件配置

在相应的 `application-{profile}.properties` 文件中设置：

```properties
# Jitsi配置
jitsi.app.id=lemomate
jitsi.domain=meeting.lemomate.com
```

### 2. 通过环境变量配置（推荐）

在生产环境中，建议使用环境变量来配置 Jitsi 域名：

```bash
# 设置环境变量
export JITSI_DOMAIN=meeting.lemomate.com
export JITSI_APP_ID=lemomate
export JWT_SECRET=your_secure_jwt_secret
export APP_DOMAIN=schedulemeet.lemomate.com
```

### 3. 在部署脚本中配置

在部署时可以通过 Java 系统属性设置：

```bash
java -jar -Dspring.profiles.active=prod \
     -Djitsi.domain=meeting.lemomate.com \
     -Djitsi.app.id=lemomate \
     /home/<USER>/lemomate.jar
```

## 配置参数说明

| 参数 | 环境变量 | 默认值 | 说明 |
|------|----------|--------|------|
| `jitsi.domain` | `JITSI_DOMAIN` | `meeting.lemomate.com` | Jitsi Meet 服务器域名 |
| `jitsi.app.id` | `JITSI_APP_ID` | `lemomate` | Jitsi 应用 ID，需与 Jitsi 服务器配置一致 |
| `jwt.secret` | `JWT_SECRET` | `ftg29918299876@` | JWT 签名密钥，需与 Jitsi 服务器配置一致 |
| `app.domain` | `APP_DOMAIN` | `schedulemeet.lemomate.com` | Lemomate 应用域名 |

## 不同环境的配置

### 开发环境
- 配置文件：`application.properties`
- Jitsi 域名：`meeting.lemomate.com`

### Docker 环境
- 配置文件：`application-docker.properties`
- Jitsi 域名：`meeting.lemomate.com`

### 生产环境
- 配置文件：`application-prod.properties`
- 支持环境变量覆盖
- Jitsi 域名：通过 `JITSI_DOMAIN` 环境变量设置

## 会议链接格式

配置完成后，系统生成的会议链接格式为：

```
https://{jitsi.domain}/{room_name}?jwt={jwt_token}
```

例如：
```
https://meeting.lemomate.com/xiangmutaolunhuiyi_1234567890?jwt=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

## 注意事项

1. **域名一致性**：确保 `jitsi.domain` 配置与实际的 Jitsi Meet 服务器域名一致
2. **JWT 密钥**：`jwt.secret` 必须与 Jitsi Meet 服务器的 JWT 配置中的密钥一致
3. **应用 ID**：`jitsi.app.id` 必须与 Jitsi Meet 服务器的应用 ID 配置一致
4. **SSL 证书**：确保 Jitsi 域名有有效的 SSL 证书

## 更新配置后的操作

修改配置后需要重启应用：

```bash
# 停止服务
systemctl stop lemomate

# 启动服务
systemctl start lemomate

# 或者重启服务
systemctl restart lemomate
```

## 验证配置

可以通过以下方式验证配置是否正确：

1. 查看应用日志确认配置加载正确
2. 创建一个测试会议，检查生成的会议链接域名
3. 尝试加入会议，确认 JWT 认证正常工作

## 故障排除

如果遇到问题，请检查：

1. Jitsi Meet 服务器是否正常运行
2. 域名解析是否正确
3. JWT 密钥是否与 Jitsi 服务器配置一致
4. 防火墙和网络配置是否正确
