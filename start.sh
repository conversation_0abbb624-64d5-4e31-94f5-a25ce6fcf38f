#!/bin/bash

# Lemomate 启动脚本
echo "正在启动 Lemomate 应用..."

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 检查Java是否安装
if ! command -v java &> /dev/null; then
    echo "错误：Java未安装，请先运行部署脚本安装环境"
    exit 1
fi

# 检查MySQL是否运行
if ! systemctl is-active --quiet mysql; then
    echo "正在启动MySQL服务..."
    systemctl start mysql
fi

# 检查Nginx是否运行
if ! systemctl is-active --quiet nginx; then
    echo "正在启动Nginx服务..."
    systemctl start nginx
fi

# 检查应用目录
if [ ! -d "/home/<USER>" ]; then
    echo "创建应用目录..."
    mkdir -p /home/<USER>/uploads/avatars
    mkdir -p /home/<USER>/logs
fi

# 检查JAR文件
if [ ! -f "/home/<USER>/lemomate.jar" ]; then
    echo "错误：找不到应用JAR文件，请先运行部署脚本"
    exit 1
fi

# 启动应用
echo "启动Lemomate后端服务..."
if systemctl is-active --quiet lemomate; then
    echo "Lemomate服务已在运行，正在重启..."
    systemctl restart lemomate
else
    echo "启动Lemomate服务..."
    systemctl start lemomate
fi

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
if systemctl is-active --quiet lemomate; then
    echo "✅ Lemomate服务已成功启动！"
    echo ""
    echo "访问地址:"
    echo "- https://schedulemeet.lemomate.com"
    echo "- http://$(curl -s ifconfig.me) (备用IP访问)"
    echo ""
    echo "维护命令:"
    echo "- 查看日志: journalctl -u lemomate -f"
    echo "- 停止服务: ./stop.sh"
    echo "- 更新应用: ./update.sh"
else
    echo "❌ 错误：Lemomate服务启动失败"
    echo "请检查日志: journalctl -u lemomate -f"
    exit 1
fi
