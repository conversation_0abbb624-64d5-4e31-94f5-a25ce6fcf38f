#!/bin/bash

# Lemomate 应用更新脚本
echo "开始更新 Lemomate 应用..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 进入项目目录
# 获取脚本所在目录作为项目目录
PROJECT_DIR="$(cd "$(dirname "$0")" && pwd)"
cd $PROJECT_DIR

echo "当前目录: $(pwd)"

# 拉取最新代码（如果使用Git）
echo "拉取最新代码..."
git pull

# 构建前端
echo "构建前端..."
cd frontend
npm run build
cd ..

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests -U

# 备份当前JAR文件
echo "备份当前JAR文件..."
cp /home/<USER>/lemomate.jar /home/<USER>/lemomate.jar.bak.$(date +%Y%m%d%H%M%S)

# 停止服务
echo "停止Lemomate服务..."
systemctl stop lemomate

# 复制新的JAR文件
echo "部署新的JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 复制前端文件
echo "部署前端文件..."
rm -rf /var/www/lemomate/*
cp -r frontend/dist/* /var/www/lemomate/

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar
chown -R www-data:www-data /var/www/lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查Lemomate服务状态..."
if systemctl is-active --quiet lemomate; then
    echo "✅ 更新成功！Lemomate服务正在运行"
    echo ""
    echo "应用已更新完成，可以正常使用所有功能"
    echo "访问地址: https://schedulemeet.lemomate.com"
else
    echo "❌ 服务启动失败，请检查日志"
    echo "查看日志命令: journalctl -u lemomate -f"
    exit 1
fi

echo ""
echo "🎉 应用更新完成！"
echo ""
echo "维护命令："
echo "- 查看日志： journalctl -u lemomate -f"
echo "- 重启服务： systemctl restart lemomate"
echo "- 停止服务： systemctl stop lemomate"
echo "- 启动服务： systemctl start lemomate"
echo "- 查看状态： systemctl status lemomate"
