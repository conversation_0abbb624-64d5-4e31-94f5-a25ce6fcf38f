# Lemomate 会议管理系统

Lemomate是一个基于Jitsi的会议管理系统，提供会议创建、管理和参与功能。

## 技术栈

- 前端：Vue.js + Element UI
- 后端：Spring Boot + Spring Security + Spring Data JPA
- 数据库：MySQL
- 视频会议：Jitsi Meet

## 快速部署

### 系统要求
- **操作系统**: Ubuntu 20.04+ 或 Debian 11/12
- **内存**: 2GB+ RAM
- **存储**: 10GB+ 可用空间
- **权限**: root或sudo权限

### 一键部署
```bash
# 1. 克隆项目
git clone <repository-url> lemomate
cd lemomate

# 2. 执行部署
sudo chmod +x deploy.sh
sudo ./deploy.sh
```

### 管理命令
```bash
sudo ./start.sh    # 启动服务
sudo ./stop.sh     # 停止服务
sudo ./update.sh   # 更新应用
sudo ./status.sh   # 查看状态
```

> 📝 **详细部署指南**: 请参考 [DEPLOY.md](DEPLOY.md) 获取完整的部署说明、故障排除和维护指南

## 用户角色

- 普通用户：参加会议
- 团队管理员：创建和管理会议，管理团队成员
- 平台管理员：管理所有用户和团队

## Jitsi集成

本项目与Jitsi Meet集成，使用以下方式：

1. 会议链接格式：`https://meeting.lemomate.com/[room_name]?jwt=[YOUR_TOKEN]`
2. JWT令牌包含用户信息（姓名、邮箱、头像）和权限信息
3. 会议房间名称基于会议标题生成，确保唯一性

要使此集成正常工作，请确保：

1. Jitsi Meet服务器已配置JWT认证
2. `.env`文件中的`JITSI_APP_ID`和`JWT_SECRET`与Jitsi配置一致

## 注意事项

- 首次部署后，请立即创建平台管理员账户
- 定期备份MySQL数据：`docker-compose exec mysql mysqldump -u root -p lemomate > backup.sql`
- 生产环境中请更改默认的JWT密钥
- 确保您的Jitsi Meet实例已正确配置JWT认证
