# Lemomate 会议管理系统

Lemomate是一个基于Jitsi的会议管理系统，提供会议创建、管理和参与功能。

## 技术栈

- 前端：Vue.js + Element UI
- 后端：Spring Boot + Spring Security + Spring Data JPA
- 数据库：MySQL
- 视频会议：Jitsi Meet

## 快速部署

### 系统要求

- **操作系统**: Debian 11/12 或 Ubuntu 20.04+
- **内存**: 至少 2GB RAM
- **存储**: 至少 10GB 可用空间
- **域名**: schedulemeet.lemomate.com（可选，也可通过IP访问）
- **Jitsi**: 需要Jitsi Meet服务器（域名为meeting.lemomate.com）

### 一键部署

1. **克隆项目到服务器**
```bash
git clone <repository-url> lemomate
cd lemomate
```

2. **执行部署脚本**
```bash
sudo chmod +x deploy.sh
sudo ./deploy.sh
```

部署脚本会自动：
- 安装所有依赖（Java 11, Ma<PERSON>, MySQL, Nginx, Node.js等）
- 配置数据库和用户
- 构建前端和后端应用
- 配置Nginx反向代理
- 获取SSL证书（如果域名已配置）
- 启动所有服务

3. **访问应用**
- 主域名: https://schedulemeet.lemomate.com
- 备用IP: http://你的服务器IP

### 管理脚本

- **启动服务**: `sudo ./start.sh`
- **停止服务**: `sudo ./stop.sh`
- **更新应用**: `sudo ./update.sh`
- **查看状态**: `sudo ./status.sh`
- **查看日志**: `journalctl -u lemomate -f`

详细的脚本使用说明请参考 [SCRIPTS.md](SCRIPTS.md)

## 用户角色

- 普通用户：参加会议
- 团队管理员：创建和管理会议，管理团队成员
- 平台管理员：管理所有用户和团队

## Jitsi集成

本项目与Jitsi Meet集成，使用以下方式：

1. 会议链接格式：`https://meeting.lemomate.com/[room_name]?jwt=[YOUR_TOKEN]`
2. JWT令牌包含用户信息（姓名、邮箱、头像）和权限信息
3. 会议房间名称基于会议标题生成，确保唯一性

要使此集成正常工作，请确保：

1. Jitsi Meet服务器已配置JWT认证
2. `.env`文件中的`JITSI_APP_ID`和`JWT_SECRET`与Jitsi配置一致

## 注意事项

- 首次部署后，请立即创建平台管理员账户
- 定期备份MySQL数据：`docker-compose exec mysql mysqldump -u root -p lemomate > backup.sql`
- 生产环境中请更改默认的JWT密钥
- 确保您的Jitsi Meet实例已正确配置JWT认证
