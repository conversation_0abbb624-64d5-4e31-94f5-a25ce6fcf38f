#!/bin/bash

# Lemomate 部署脚本
echo "开始部署Lemomate应用到VPS..."

# 错误处理
set -e

# 检查是否以root运行
if [ "$(id -u)" -ne 0 ]; then
    echo "错误：请以root用户运行此脚本（使用sudo）"
    exit 1
fi

# 检查系统类型
if [ -f /etc/debian_version ]; then
    echo "检测到Debian/Ubuntu系统"
elif [ -f /etc/redhat-release ]; then
    echo "错误：检测到RedHat/CentOS系统，此脚本仅支持Debian/Ubuntu"
    exit 1
else
    echo "警告：未知系统类型，脚本可能不兼容"
    read -p "是否继续？ (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 预检查
echo "执行预检查..."

# 检查网络连接
if ! ping -c 1 google.com &> /dev/null && ! ping -c 1 baidu.com &> /dev/null; then
    echo "警告：网络连接不可用，可能影响软件安装"
    read -p "是否继续？ (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查磁盘空间
AVAILABLE_SPACE=$(df / | awk 'NR==2{print $4}')
REQUIRED_SPACE=10485760  # 10GB in KB
if [ "$AVAILABLE_SPACE" -lt "$REQUIRED_SPACE" ]; then
    echo "警告：可用磁盘空间不足 ($((AVAILABLE_SPACE/1024/1024))GB)，建议至少需要 10GB"
    read -p "是否继续？ (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

# 检查内存
TOTAL_MEM=$(free -m | awk 'NR==2{print $2}')
if [ "$TOTAL_MEM" -lt 1800 ]; then
    echo "警告：系统内存不足 (${TOTAL_MEM}MB)，建议至少需要 2GB"
    read -p "是否继续？ (y/n) " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "✅ 预检查通过"

# 确保目录存在
echo "创建必要的目录..."
mkdir -p /home/<USER>/uploads/avatars
mkdir -p /home/<USER>/logs

# 安装必要的软件
echo "安装必要的软件..."

# 设置非交互式安装
export DEBIAN_FRONTEND=noninteractive

# 更新软件包列表
echo "更新软件包列表..."
apt-get update

# 安装基本工具
echo "安装基本工具..."
apt-get install -y curl wget gnupg2 software-properties-common apt-transport-https ca-certificates

# 安装Java 11
echo "安装OpenJDK 11..."

# 检查是否已安装Java
if command -v java &> /dev/null; then
    JAVA_VERSION=$(java -version 2>&1 | head -n1 | awk -F'"' '{print $2}' | awk -F'.' '{print $1}')
    if [ "$JAVA_VERSION" = "11" ]; then
        echo "Java 11已安装，跳过安装步骤"
    else
        echo "检测到Java $JAVA_VERSION，需要安装Java 11"
    fi
else
    echo "Java未安装，开始安装Java 11"
fi

# 安装OpenJDK 11
if ! (command -v java &> /dev/null && java -version 2>&1 | grep -q "11\."); then
    echo "安装OpenJDK 11..."
    apt-get install -y openjdk-11-jdk

    # 如果安装失败，尝试从其他源安装
    if ! command -v java &> /dev/null; then
        echo "默认安装失败，尝试从其他源安装..."

        # 添加Adoptium仓库
        wget -O - https://packages.adoptium.net/artifactory/api/gpg/key/public | gpg --dearmor | tee /etc/apt/trusted.gpg.d/adoptium.gpg > /dev/null
        echo "deb https://packages.adoptium.net/artifactory/deb $(awk -F= '/^VERSION_CODENAME/{print$2}' /etc/os-release) main" | tee /etc/apt/sources.list.d/adoptium.list

        apt-get update
        apt-get install -y temurin-11-jdk
    fi
fi

# 验证Java安装
echo "验证Java安装..."
java -version

# 安装Maven
echo "安装Maven..."
apt-get install -y maven

# 验证Maven安装
echo "验证Maven安装..."
mvn -version

# 安装MySQL
echo "安装MySQL..."
# 设置非交互式安装
export DEBIAN_FRONTEND=noninteractive
apt-get install -y mysql-server

# 启动MySQL服务
echo "启动MySQL服务..."
systemctl start mysql
systemctl enable mysql

# 等待MySQL启动
echo "等待MySQL服务启动..."
sleep 5

# 安装Nginx
echo "安装Nginx..."
apt-get install -y nginx

# 启动Nginx服务
echo "启动Nginx服务..."
systemctl start nginx
systemctl enable nginx

# 安装Certbot（用于SSL证书）
echo "安装Certbot..."
apt-get install -y certbot python3-certbot-nginx

# 安装Node.js和npm
echo "安装Node.js和npm..."

# 检查是否已安装Node.js
if ! command -v node &> /dev/null; then
    echo "Node.js未安装，正在安装..."

    # 安装NodeSource仓库
    curl -fsSL https://deb.nodesource.com/setup_16.x | bash -

    # 安装Node.js
    apt-get install -y nodejs
else
    echo "Node.js已安装"
fi

# 验证Node.js和npm安装
echo "验证Node.js和npm安装..."
node -v
npm -v

# 配置MySQL
echo "配置MySQL..."

# 生成随机密码
DB_PASSWORD=$(openssl rand -base64 32)
echo "生成的数据库密码: $DB_PASSWORD"

# 检查MySQL是否正在运行
if ! systemctl is-active --quiet mysql; then
    echo "错误：MySQL服务未启动"
    exit 1
fi

# 在Ubuntu上，MySQL初始安装后可能没有root密码，使用sudo访问
echo "设置MySQL root密码..."
sudo mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';" 2>/dev/null || \
mysql -e "ALTER USER 'root'@'localhost' IDENTIFIED WITH mysql_native_password BY '$DB_PASSWORD';" 2>/dev/null || \
echo "警告：无法设置root密码，将使用默认访问方式"

# 创建数据库和用户
echo "创建数据库和用户..."
sudo mysql -e "CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;" 2>/dev/null || \
mysql -e "CREATE DATABASE IF NOT EXISTS lemomate CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;"

sudo mysql -e "CREATE USER IF NOT EXISTS 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';" 2>/dev/null || \
mysql -e "CREATE USER IF NOT EXISTS 'lemomate'@'localhost' IDENTIFIED BY '$DB_PASSWORD';"

sudo mysql -e "GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';" 2>/dev/null || \
mysql -e "GRANT ALL PRIVILEGES ON lemomate.* TO 'lemomate'@'localhost';"

sudo mysql -e "FLUSH PRIVILEGES;" 2>/dev/null || \
mysql -e "FLUSH PRIVILEGES;"

# 更新数据库配置
echo "更新数据库配置..."
sed -i 's/spring.datasource.username=root/spring.datasource.username=lemomate/g' src/main/resources/application-prod.properties
sed -i "s/spring.datasource.password=root/spring.datasource.password=$DB_PASSWORD/g" src/main/resources/application-prod.properties

# 构建后端
echo "构建后端应用..."
mvn clean package -DskipTests

# 构建前端
echo "构建前端应用..."
cd frontend

# 检查Node.js和npm版本
echo "Node.js版本:"
node -v
echo "npm版本:"
npm -v

# 安装依赖
echo "安装前端依赖..."
npm install

# 构建前端
echo "构建前端..."
npm run build

cd ..

# 配置Nginx
echo "配置Nginx..."
cat > /etc/nginx/sites-available/lemomate << EOF
# Lemomate应用配置
server {
    listen 80 default_server;
    server_name schedulemeet.lemomate.com _;

    location / {
        root /home/<USER>/frontend;
        index index.html;
        try_files \$uri \$uri/ /index.html;
    }

    location /api/ {
        proxy_pass http://localhost:8085;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    location /api/uploads/ {
        alias /home/<USER>/uploads/;
        autoindex off;
    }
}
EOF

# 启用站点配置
echo "启用Nginx站点配置..."
ln -sf /etc/nginx/sites-available/lemomate /etc/nginx/sites-enabled/

# 删除默认配置
rm -f /etc/nginx/sites-enabled/default

# 检查Nginx配置
echo "检查Nginx配置..."
nginx -t

# 重启Nginx
echo "重启Nginx..."
systemctl restart nginx

# 获取SSL证书（可选，如果域名已配置）
echo "尝试获取SSL证书..."
if ping -c 1 schedulemeet.lemomate.com &> /dev/null; then
    echo "域名可访问，获取SSL证书..."
    certbot --nginx -d schedulemeet.lemomate.com --non-interactive --agree-tos --email <EMAIL> || echo "SSL证书获取失败，将使用HTTP访问"
else
    echo "域名未配置或不可访问，跳过SSL证书获取"
fi

# 复制前端文件
echo "部署前端文件..."
mkdir -p /home/<USER>/frontend
cp -r frontend/dist/* /home/<USER>/frontend/

# 创建服务文件
echo "创建系统服务..."
cat > /etc/systemd/system/lemomate.service << EOF
[Unit]
Description=Lemomate Backend Service
After=network.target mysql.service

[Service]
User=root
WorkingDirectory=/home/<USER>
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
SuccessExitStatus=143
TimeoutStopSec=10
Restart=on-failure
RestartSec=5

[Install]
WantedBy=multi-user.target
EOF

# 复制JAR文件
echo "复制JAR文件..."
cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar

# 设置权限
echo "设置文件权限..."
chmod 755 /home/<USER>/lemomate.jar
chown -R www-data:www-data /home/<USER>/frontend

# 重新加载系统服务
echo "重新加载系统服务..."
systemctl daemon-reload

# 启用服务
echo "启用Lemomate服务..."
systemctl enable lemomate

# 启动服务
echo "启动Lemomate服务..."
systemctl start lemomate

# 等待服务启动
echo "等待服务启动..."
sleep 10

# 检查服务状态
echo "检查Lemomate服务状态..."
systemctl status lemomate

# 检查服务是否成功启动
if systemctl is-active --quiet lemomate; then
    echo ""
    echo "🎉 恭喜！部署完成！"
    echo ""
    echo "应用访问信息："
    echo "- 应用地址: https://schedulemeet.lemomate.com (如果SSL配置成功)"
    echo "- 备用地址: http://$(curl -s ifconfig.me) (通过服务器IP访问)"
    echo "- 后端API: http://$(curl -s ifconfig.me)/api"
    echo ""
    echo "数据库信息："
    echo "- 数据库名: lemomate"
    echo "- 用户名: lemomate"
    echo "- 密码: $DB_PASSWORD"
    echo ""
    echo "维护命令："
    echo "- 查看日志: journalctl -u lemomate -f"
    echo "- 重启服务: systemctl restart lemomate"
    echo "- 停止服务: systemctl stop lemomate"
    echo "- 启动服务: systemctl start lemomate"
    echo "- 查看状态: systemctl status lemomate"
    echo ""
    echo "注意事项："
    echo "1. 请确保域名 schedulemeet.lemomate.com 已正确解析到服务器IP"
    echo "2. 请妥善保存数据库密码"
    echo "3. 如需更新应用，请使用 ./update.sh 脚本"
else
    echo ""
    echo "❌ 警告：Lemomate服务启动失败！"
    echo "请检查日志以获取更多信息: journalctl -u lemomate -f"
    exit 1
fi
