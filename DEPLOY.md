# Lemomate 部署指南

## 系统要求

### 支持的操作系统
- Ubuntu 20.04+ LTS
- Debian 11/12

### 硬件要求
- **CPU**: 1核心（推荐2核心）
- **内存**: 2GB RAM（推荐4GB）
- **存储**: 10GB可用空间（推荐20GB）
- **网络**: 稳定的互联网连接

### 必要条件
- root权限或sudo权限
- 域名 `schedulemeet.lemomate.com`（可选，也可通过IP访问）
- Jitsi Meet服务器（域名为 `meeting.lemomate.com`）

## 快速部署

### 1. 下载项目
```bash
git clone <repository-url> lemomate
cd lemomate
```

### 2. 一键部署
```bash
# 设置权限并部署
sudo chmod +x deploy.sh
sudo ./deploy.sh
```

### 3. 部署过程
脚本会自动执行：
- 系统检查（网络、磁盘、内存）
- 软件安装（Java 11, Maven, MySQL, Nginx, Node.js）
- 数据库配置
- 应用构建和部署
- 服务配置和启动
- SSL证书配置（如果域名可用）

### 4. 部署完成
成功后显示：
```
🎉 恭喜！部署完成！

应用访问信息：
- 应用地址: https://schedulemeet.lemomate.com
- 备用地址: http://YOUR_SERVER_IP
- 后端API: http://YOUR_SERVER_IP/api

数据库信息：
- 数据库名: lemomate
- 用户名: lemomate
- 密码: [随机生成的密码]
```

**重要**: 请保存显示的数据库密码！

## 管理脚本

### 可用脚本
- `deploy.sh` - 首次部署
- `start.sh` - 启动服务
- `stop.sh` - 停止服务
- `update.sh` - 更新应用
- `status.sh` - 查看状态

### 使用方法

#### 方式1: 使用Makefile（推荐）
```bash
make setup    # 设置脚本权限
make deploy   # 首次部署
make start    # 启动服务
make stop     # 停止服务
make update   # 更新应用
make status   # 查看状态
make clean    # 清理构建文件
```

#### 方式2: 直接运行脚本
```bash
sudo ./deploy.sh   # 首次部署
sudo ./start.sh    # 启动服务
sudo ./stop.sh     # 停止服务
sudo ./update.sh   # 更新应用
sudo ./status.sh   # 查看状态
```

### 系统服务管理
```bash
# 查看服务状态
systemctl status lemomate

# 重启服务
systemctl restart lemomate

# 查看日志
journalctl -u lemomate -f
```

## 故障排除

### 常见问题

#### 1. 部署失败：网络连接问题
```bash
# 检查网络
ping google.com
ping baidu.com

# 检查DNS
cat /etc/resolv.conf
```

#### 2. Java安装失败
```bash
# 手动安装Java 11
sudo apt update
sudo apt install openjdk-11-jdk
java -version
```

#### 3. MySQL配置问题
```bash
# 检查MySQL状态
systemctl status mysql

# 重启MySQL
systemctl restart mysql

# 查看MySQL日志
tail -f /var/log/mysql/error.log
```

#### 4. 端口被占用
```bash
# 检查端口占用
netstat -tlnp | grep 8085
netstat -tlnp | grep 80

# 杀死占用进程
sudo kill -9 <PID>
```

#### 5. 权限问题
```bash
# 修复文件权限
sudo chown -R www-data:www-data /var/www/lemomate
sudo chmod 755 /home/<USER>/lemomate.jar
```

#### 6. 内存不足
```bash
# 创建交换文件
sudo fallocate -l 2G /swapfile
sudo chmod 600 /swapfile
sudo mkswap /swapfile
sudo swapon /swapfile
```

### 日志查看
```bash
# 应用日志
journalctl -u lemomate -f

# Nginx日志
tail -f /var/log/nginx/access.log
tail -f /var/log/nginx/error.log

# MySQL日志
tail -f /var/log/mysql/error.log
```

## 安全配置

### 防火墙设置
```bash
# 安装UFW
sudo apt install ufw

# 配置规则
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443

# 启用防火墙
sudo ufw enable
```

### 定期备份
```bash
# 备份数据库
mysqldump -u lemomate -p lemomate > backup_$(date +%Y%m%d).sql

# 备份上传文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz /home/<USER>/uploads/
```

## 性能优化

### JVM内存调优
编辑 `/etc/systemd/system/lemomate.service`：
```ini
ExecStart=/usr/bin/java -jar -Xmx1g -Xms512m -Dspring.profiles.active=prod /home/<USER>/lemomate.jar
```

### MySQL优化
编辑 `/etc/mysql/mysql.conf.d/mysqld.cnf`：
```ini
[mysqld]
innodb_buffer_pool_size = 512M
max_connections = 100
```

## 更新应用

### 日常更新
```bash
# 使用更新脚本（推荐）
sudo ./update.sh

# 或手动更新
git pull
sudo systemctl stop lemomate
mvn clean package -DskipTests
cd frontend && npm run build && cd ..
sudo cp target/lemomate-0.0.1-SNAPSHOT.jar /home/<USER>/lemomate.jar
sudo cp -r frontend/dist/* /var/www/lemomate/
sudo systemctl start lemomate
```

### 回滚版本
```bash
# 恢复备份的JAR文件
sudo cp /home/<USER>/lemomate.jar.bak.YYYYMMDDHHMMSS /home/<USER>/lemomate.jar
sudo systemctl restart lemomate
```

## 监控和维护

### 系统监控
```bash
# 查看系统资源
htop
df -h
free -h

# 查看应用状态
sudo ./status.sh
```

### 定期维护
```bash
# 系统更新
sudo apt update && sudo apt upgrade

# 清理日志
sudo journalctl --vacuum-time=30d

# 清理临时文件
sudo apt autoremove
sudo apt autoclean
```

## 文件结构

```
lemomate/
├── deploy.sh          # 部署脚本
├── start.sh           # 启动脚本
├── stop.sh            # 停止脚本
├── update.sh          # 更新脚本
├── status.sh          # 状态检查脚本
├── Makefile           # Make命令
└── DEPLOY.md          # 本文档

部署后的文件位置：
├── /home/<USER>/
│   ├── lemomate.jar           # 应用JAR文件
│   ├── uploads/               # 上传文件目录
│   └── logs/                  # 日志目录
├── /var/www/lemomate/         # 前端文件
├── /etc/nginx/sites-available/lemomate  # Nginx配置
└── /etc/systemd/system/lemomate.service # 系统服务
```

## 访问应用

### 通过域名（推荐）
- https://schedulemeet.lemomate.com

### 通过IP
- http://YOUR_SERVER_IP

### 管理界面
- 使用平台管理员账号登录后可访问管理功能

## 技术支持

遇到问题时：

1. **查看日志**：`journalctl -u lemomate -f`
2. **检查状态**：`sudo ./status.sh`
3. **收集信息**：
   ```bash
   uname -a
   lsb_release -a
   free -h
   df -h
   ```
4. **联系支持**：提供错误日志和系统信息

---

**注意事项**：
- 部署前确保服务器满足系统要求
- 生产环境部署前建议先在测试环境验证
- 定期备份数据库和重要文件
- 保持系统和应用的定期更新
